{"Login": {"title": "Casino Admin", "welcome": "WELCOME!!", "subtitle": "Grow your business and income in a simple, effective, and profitable way", "signIn": "Sign in to your account", "username": "Username", "password": "Password", "rememberMe": "Remember me", "signInButton": "Sign in", "signingIn": "Signing in...", "noAccount": "Don't have an account?", "contactAdmin": "Contact administrator", "enterUsername": "Enter your username", "enterPassword": "Enter your password", "invalidUsernameOrPassword": "Invalid username or password", "playersNotAllowed": "Players are not allowed to access the admin panel.", "invalidTokenFormat": "Invalid token format received from server", "failedToStoreAuthData": "Failed to store authentication data. Please try again."}, "Layout": {"balance": "Balance", "profile": "Profile", "logout": "Logout", "waitBeforeRefresh": "Please wait less than 60 seconds before refreshing balance again", "failedToUpdateBalance": "Failed to update balance"}, "Dashboard": {"dashboardOverview": "Dashboard Overview", "welcomeMessage": "Welcome to your casino management dashboard", "balanceStats": "Balance stats", "today": "Today:", "sold": "Sold", "soldTooltip": "Total funds transferred to your direct sub-accounts today", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "depositTooltip": "Funds received from your parent account today", "withdraw": "Withdraw", "withdrawTooltip": "Funds withdrawn from your direct sub-accounts today", "seeTransactions": "See Transactions →", "totalGames": "Total Games", "seeAllGames": "See All Games →", "totalUsers": "Total Users", "seeAllUsers": "See All Users →", "active": "Active", "banned": "Banned", "lastTransactions": "Last Transactions", "added": "Added", "deducted": "Deducted", "noTransactionRecords": "No transaction records", "lastBannedPlayers": "Last Banned Players", "noUsersBanned": "No users are currently banned", "topOwners": "Top Owners", "topSuperadmins": "Top Superadmins", "topAdmins": "Top Admins", "topCashiers": "Top Cashiers", "topPlayers": "Top Players", "balance": "Balance", "noSuperownerRecords": "No superowner records", "noOwnerRecords": "No owner records", "noSuperadminRecords": "No superadmin records", "noAdminRecords": "No admin records", "noCashierRecords": "No cashier records", "noPlayerRecords": "No player records", "failedToLoad": "Failed to load dashboard data", "superadmin": "Superadmin", "cashier": "Cashier", "player": "Player"}, "Betting-history": {"title": "Betting History", "description": "View user betting history", "searchPlaceholder": "Search by username (min. 4 characters)", "username": "Username", "role": "Role", "balance": "Balance", "status": "Status", "createdAt": "Created At", "actions": "Actions", "history": "History", "banned": "Banned", "active": "Active", "previous": "< Previous", "next": "Next >", "showing": "Showing", "to": "to", "of": "of", "results": "results"}, "Game-list": {"gamesManagement": "Games Management", "manageAndView": "Manage and view game listings", "gamesDatabase": "Games Database", "initializeGamesDatabase": "Initialize the games database", "initializeDatabase": "Initialize Database", "gamesList": "Games List", "viewAndManage": "View and manage game listings", "searchGames": "Search games by name (min. 3 characters)", "allProviders": "All Providers", "allCategories": "All Categories", "id": "ID", "name": "Name", "provider": "Provider", "image": "Image", "categories": "Categories", "loadingGames": "Loading games...", "noGamesAvailable": "No games available", "previous": "< Previous", "next": "Next >", "showing": "Showing", "to": "to", "of": "of", "results": "results", "fullSizeGame": "Full Size Game", "close": "X", "failedToFetchGames": "Failed to fetch games", "databaseInitializedSuccessfully": "Database initialized successfully", "failedToInitializeDatabase": "Failed to initialize database"}, "myProfile": {"myProfile": "My Profile", "profileInformation": "View and manage your profile information", "username": "Username", "role": "Role", "balance": "Balance", "accountStatus": "Account Status", "accountIsActive": "Account is active"}, "password-form": {"changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "changePasswordButton": "Change Password", "changingPassword": "Changing Password...", "passwordRequirements": "Password requirements:", "atLeast8Characters": "At least 8 characters", "uppercaseLowercaseLetters": "Uppercase & lowercase letters", "atLeastOneNumber": "At least one number", "currentPasswordRequired": "Current password is required", "newPasswordRequired": "New password is required", "passwordTooShort": "Password must be at least 8 characters", "passwordComplexity": "Password must contain at least one lowercase character, and at least one number", "confirmPasswordRequired": "Please confirm your password", "passwordsDoNotMatch": "Passwords do not match", "passwordChangedSuccessfully": "Password changed successfully", "pleaseLoginAgain": "Please login again."}, "myTransactions": {"myTransactions": "My Transactions", "viewAndManage": "View and manage your transaction history", "refreshTransactions": "Refresh transactions", "toggleFilters": "Toggle filters", "type": "Type", "all": "All", "add": "Add", "deduct": "Deduct", "startDate": "Start Date", "endDate": "End Date", "clear": "Clear", "id": "ID", "amount": "Amount", "from": "From", "to": "To", "date": "Date", "noTransactionsFound": "No transactions found", "previous": "< Previous", "next": "Next >", "showing": "Showing", "toText": "to", "of": "of", "results": "results"}, "userTransactions": {"title": "User Transactions", "description": "View and manage user transaction history", "searchPlaceholder": "Search users (min. 4 characters)", "hideFilters": "Hide Filters", "showFilters": "Show Filters", "roleFilter": "Role Filter", "statusFilter": "Status Filter", "allUsers": "All Users", "activeUsers": "Active Users", "bannedUsers": "Banned Users", "dateRange": "Date Range", "startDate": "Start Date", "endDate": "End Date", "clear": "Clear", "username": "Username", "role": "Role", "balance": "Balance", "status": "Status", "createdAt": "Created At", "actions": "Actions", "viewHistory": "View History", "noUsersFound": "No users found", "previous": "< Previous", "next": "Next >", "showing": "Showing", "toText": "to", "of": "of", "results": "results", "active": "Active", "banned": "Banned"}, "transactionHistoryModal": {"title": "Transaction History", "amount": "Amount", "from": "From", "type": "Type", "to": "To", "date": "Date", "noTransactionsFound": "No transactions found", "previous": "< Previous", "next": "Next >", "showing": "Showing", "of": "of", "results": "results", "clear": "Clear", "transactionHistory": "Transaction History - {username}"}, "userBanManagement": {"banManagement": "Ban Management", "manageUserAccess": "Manage user access and restrictions", "searchUsers": "Search users (min. 4 characters)", "username": "Username", "role": "Role", "balance": "Balance", "status": "Status", "createdAt": "Created At", "actions": "Actions", "unban": "<PERSON><PERSON>", "ban": "Ban", "previous": "< Previous", "next": "Next >", "showing": "Showing", "to": "to", "of": "of", "results": "results", "banned": "Banned", "active": "Active"}, "userBanManagementModal": {"unbanUser": "Unban User", "banUser": "Ban User", "userInformation": "User Information", "thisActionWillUnban": "This action will unban this user and all their descendants.", "thisActionWillBan": "This action will ban this user and all their descendants.", "cancel": "Cancel", "processing": "Processing...", "userUnbanned": "{username} and descendants have been unbanned", "userBanned": "{username} and descendants have been banned", "banStatusUpdateFailed": "Failed to update ban status"}, "manageCashPage": {"cashManagement": "Cash Management", "efficientlyManage": "Efficiently manage user balances and transactions", "searchByUsername": "Search by username (min. 4 characters)", "username": "Username", "role": "Role", "balance": "Balance", "status": "Status", "createdAt": "Created At", "actions": "Actions", "add": "Add", "deduct": "Deduct", "previous": "< Previous", "next": "Next >", "showing": "Showing", "to": "to", "of": "of", "results": "results", "banned": "Banned", "active": "Active"}, "manageCashModal": {"addFunds": "Add Funds", "deductFunds": "Deduct Funds", "transferFunds": "Transfer funds from your account to user", "deductFundsFromUser": "Deduct funds from user to your account", "currentBalance": "Current Balance", "amountTo": "Amount to", "add": "Add", "deduct": "Deduct", "newUserBalance": "New User Balance", "yourNewBalance": "Your New Balance", "deductedFromYourBalance": "This amount will be deducted from your balance", "addedToYourBalance": "This amount will be added to your balance", "cancel": "Cancel", "confirm": "Confirm", "processing": "Processing...", "amountRequired": "Amount is required", "validNumber": "Please enter a valid number", "amountGreaterThan": "Amount must be greater than 0.99", "insufficientBalance": "Insufficient balance", "addSuccess": "Successfully added cash", "deductSuccess": "Successfully deducted cash", "transactionFailed": "Failed to process transaction"}, "createUserPage": {"title": "Create New User", "description": "Add new users and assign their roles in the system", "usernameLabel": "Username", "usernamePlaceholder": "Enter username (5-20 characters)", "usernameRequirements": "Only letters, numbers, and underscores allowed", "roleLabel": "Role", "passwordLabel": "Password", "passwordRequirements": "Minimum required :", "createUserButton": "Create User", "creating": "Creating...", "userCreatedSuccessfully": "User Created Successfully", "newAccountCreated": "New {role} account has been created", "usernameLengthError": "Username must be between 5 and 20 characters", "usernameFormatError": "Username can only contain letters, numbers, and underscores", "passwordLengthError": "Password must be at least 8 characters", "passwordComplexityError": "Password must contain at least one letter and one number", "roleRequiredError": "Role is required", "currencyLengthError": "Currency must be 3 characters", "atLeast8Characters": "At least 8 characters", "uppercaseLowercaseLetters": "Uppercase & lowercase letters", "atLeastOneNumber": "At least one number"}, "userSearchPage": {"usersManagement": "Users Management", "searchAndManage": "Search and manage users", "searchUsersPlaceholder": "Search users (min. 4 characters)", "hideFilters": "Hide Filters", "showFilters": "Show Filters", "roleFilter": "Role Filter", "statusFilter": "Status Filter", "allUsers": "All Users", "activeUsers": "Active Users", "bannedUsers": "Banned Users", "dateRange": "Date Range", "startDate": "Start Date", "endDate": "End Date", "clear": "Clear", "username": "Username", "role": "Role", "balance": "Balance", "status": "Status", "createdAt": "Created At", "actions": "Actions", "manage": "Manage", "noUsersFound": "No users found", "previous": "< Previous", "next": "Next >", "showing": "Showing", "to": "to", "of": "of", "results": "results", "banned": "Banned", "active": "Active", "all": "All"}, "footerComponent": {"copyRight": ".", "allRightsReserved": "©"}, "sidebarMenu": {"dashboard": "Dashboard", "users": "Users", "createUser": "Create User", "manageUserCash": "Manage User Cash", "banUnbanUsers": "Ban/Unban Users", "allUsers": "All Users", "transactions": "Transactions", "myTransactions": "My Transactions", "userTransactions": "User Transactions", "games": "Games", "gameList": "Game List", "bettingHistory": "Betting History", "myProfile": "My profile", "online": "Online"}, "gameHistoryModal": {"title": "Game History", "from": "From", "to": "To", "clear": "Clear", "game": "Game", "bet": "Bet", "win": "Win", "action": "Action", "date": "Date", "balanceBefore": "Balance Before", "balanceAfter": "Balance After", "previous": "Previous", "page": "Page", "of": "of", "next": "Next", "close": "Close", "userNotAuthenticated": "User not authenticated", "failedToFetchGameHistory": "Failed to fetch game history"}, "initializeDatabaseModal": {"initializeDatabase": "Initialize Database", "warningMessage": "This action will initialize the game database. This cannot be undone.", "cancel": "Cancel", "initialize": "Initialize", "processing": "Processing...", "databaseInitializedSuccessfully": "Database initialized successfully", "failedToInitializeDatabase": "Failed to initialize database"}, "refreshButtonUi": {"balanceUpdated": "Balance updated successfully", "BalanceRefreshfailed": "Balance refresh failed"}, "BanManagementTab": {"thisActionWillUnbanDescendants": "This action will unban this user and all their descendants.", "thisActionWillUnban": "This action will unban this user.", "thisActionWillBanDescendants": "This action will ban this user and all their descendants.", "thisActionWillBan": "This action will ban this user.", "unbanUserDescendants": "Unban User & Descendants", "unbanUser": "Unban User", "banUserDescendants": "Ban User & Descendants", "banUser": "Ban User", "userUnbanned": "{username} and descendants have been unbanned", "userBanned": "{username} and descendants have been banned", "banStatusUpdateFailed": "Failed to update ban status", "currentlyBanned": "Currently Banned", "currentlyActive": "Currently Active", "processing": "Processing..."}, "BettingHistoryTab": {"from": "From", "to": "To", "clear": "Clear", "game": "Game", "bet": "Bet", "win": "Win", "action": "Action", "date": "Date", "balanceBefore": "Balance Before", "balanceAfter": "Balance After", "noGameHistoryFound": "No game history found", "showingPage": "Showing page", "ofText": "of", "previous": "Previous", "next": "Next", "failedToFetchGameHistory": "Failed to fetch game history", "userNotAuthenticated": "User not authenticated"}, "CashManagementTab": {"validNumber": "Please enter a valid number", "amountGreaterThan": "Amount must be greater than 0.99", "insufficientBalance": "Insufficient balance", "addSuccess": "Cash added successfully", "addFailed": "Failed to add cash", "deductSuccess": "Cash deducted successfully", "deductFailed": "Failed to deduct cash", "unavailableTitle": "Cash Management Unavailable", "unavailableDescription": "You cannot manage this user's balance while they are banned. Unban the user first to perform cash operations.", "addCashTitle": "Add Cash", "amountToAddLabel": "Amount to Add", "enterAmountPlaceholder": "Enter amount", "previewBalanceLabel": "Preview Balance", "processing": "Processing...", "addCashButton": "Add Cash", "deductCashTitle": "Deduct Cash", "amountToDeductLabel": "Amount to Deduct", "deductCashButton": "Deduct Cash"}, "TransactionHistoryTab": {"from": "From", "to": "To", "clear": "Clear", "type": "Type", "amount": "Amount", "date": "Date", "noTransactionsFound": "No transactions found", "showingPage": "Showing page", "ofText": "of", "previous": "Previous", "next": "Next", "failedToFetchTransactions": "Failed to fetch transactions"}, "UserManageModal": {"manageUser": "Manage User", "basicInformation": "Basic Information", "username": "Username", "role": "Role", "status": "Status", "banned": "Banned", "active": "Active", "inactive": "Inactive", "accountDetails": "Account Details", "balance": "Balance", "lastLogin": "Last Login", "createdAt": "Created At", "childrens": "Childrens", "hisResponsible": "Manager name", "userInfo": "User Info", "manageCash": "Manage Cash", "banManagement": "Ban Management", "transactions": "Transactions", "bettingHistory": "Betting History", "tree": "Tree", "failedToFetchProfile": "Failed to fetch user profile", "info": "User Info", "cash": "Manage Cash", "ban": "Ban Management", "betting": "Betting History"}, "UserTreeTab": {"failedToFetchTreeData": "Failed to fetch tree data", "banned": "Banned", "balance": "Balance", "manageUser": "Manage user", "userTree": "User Tree", "searchUsersPlaceholder": "Search users...", "minBalance": "Min balance", "allStatuses": "All Statuses", "bannedOnly": "Banned Only", "activeOnly": "Active Only", "noMatchingUsers": "No matching users found"}}