@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-dark: #0a0a0f;
  --color-mid: #1a1b26;
  --color-light: #a1a1aa;
  --color-accent: #6784bb;
  
  --sidebar-bg: var(--color-dark);
  --card-bg: var(--color-mid);
  --text-primary: white;
  --text-secondary: var(--color-light);
}

/* Login Page Specific Styles */
.login-page {
  --bg-primary: #1e293b;
  --bg-secondary: #1e293b;
  --login-right-bg: #243748;
  --text-dark: #e2e8f0;
  --text-light: #94a3b8;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Base Styles */
body {
  @apply bg-gray-50 text-gray-900 antialiased;
  font-feature-settings: "cv02", "cv03", "cv04", "cv11";
}


/* Card Styles */
.dashboard-card {
  @apply bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200;
  backdrop-filter: blur(10px);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Hover Effects */
.hover-lift {
  @apply transition-transform duration-300 ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Glass Effect */
.glass-effect {
  @apply bg-white bg-opacity-70 backdrop-blur-lg;
}

/* Custom Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-10px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Gradient Text */
.gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-[var(--color-mid)] to-[var(--color-dark)];
}

/* Card Hover Effects */
.card-hover {
  @apply transition-all duration-300 hover:shadow-lg hover:scale-[1.02];
}

/* Button Styles */
.btn-primary {
  @apply px-4 py-2 bg-blue-600 text-white rounded-lg 
         transition-all duration-300 ease-in-out
         hover:bg-blue-700 hover:shadow-md
         active:scale-95 active:shadow-sm
         focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50;
}

/* Sidebar Styles */
.sidebar-link {
  @apply flex items-center px-4 py-2 text-gray-300 
         rounded-lg transition-all duration-200 ease-in-out
         hover:bg-gray-700 hover:text-white;
}

.sidebar-link.active {
  @apply bg-blue-600 text-white;
}

/* Header Styles */
.header-shadow {
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.04);
}

/* Dashboard Grid */
.dashboard-grid {
  @apply grid gap-6;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* Stats Card */
.stats-card {
  @apply p-6 rounded-xl bg-white shadow-sm 
         transition-all duration-300 
         hover:shadow-md hover:translate-y-[-2px];
}

/* Loading Animation */
.loading-pulse {
  @apply animate-pulse bg-gray-100 rounded-lg;
}

/* Status Indicators */
.status-dot {
  @apply w-2 h-2 rounded-full;
}

.status-dot.online {
  @apply bg-green-500;
}

.status-dot.offline {
  @apply bg-red-500;
}

/* Table Styles */
.table-row {
  @apply hover:bg-gray-50 transition-colors duration-150;
}

/* Form Elements */
.input-field {
  @apply w-full px-4 py-2 rounded-lg border border-gray-300 
         focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
         transition-all duration-200;
}

/* Tooltip */
.tooltip {
  @apply invisible absolute;
}

.has-tooltip:hover .tooltip {
  @apply visible z-50;
}

/* Chart Container */
.chart-container {
  @apply p-4 bg-white rounded-xl shadow-sm;
}

/* Custom Select Styles */
.custom-select-wrapper {
  @apply relative flex-1;
}

.custom-select {
  @apply block w-full pl-3 pr-8 py-2 border border-gray-300 rounded-md 
         leading-5 bg-white text-gray-700 focus:outline-none 
         focus:ring-1 focus:ring-blue-500 focus:border-blue-500 
         appearance-none cursor-pointer;
}

.custom-select-icon {
  @apply absolute inset-y-0 left-0 pl-3 flex items-center 
         pointer-events-none text-gray-400;
}

.custom-select-arrow {
  @apply absolute inset-y-0 right-0 pr-3 flex items-center 
         pointer-events-none text-gray-400;
}

.custom-select:hover {
  @apply border-gray-400 shadow-sm;
}

.custom-select:focus {
  @apply ring-1 ring-blue-500 border-blue-500;
}

.custom-select:disabled {
  @apply bg-gray-100 cursor-not-allowed opacity-75;
}

/* Responsive Typography */
@screen sm {
  h1 {
    @apply text-2xl;
  }
  h2 {
    @apply text-xl;
  }
}

@screen md {
  h1 {
    @apply text-3xl;
  }
  h2 {
    @apply text-2xl;
  }
}

@screen lg {
  h1 {
    @apply text-4xl;
  }
  h2 {
    @apply text-3xl;
  }
}

@layer utilities {
  .backdrop-blur-modal {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
}
