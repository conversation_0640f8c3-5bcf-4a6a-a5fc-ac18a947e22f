'use client';

import { useState } from 'react';
import { authService } from '@/services/auth.service';
import { motion } from 'framer-motion';
import { FaEye, FaEyeSlash, FaKey } from 'react-icons/fa';
import { userService } from '@/services/user.service';
import { toast } from 'sonner';
import { authUtils } from '@/utils/auth.utils';
import { useTranslations } from 'next-intl';

interface ChangePasswordFormProps {
  userId: number;
  username?: string;
  onSuccess?: () => void;
}

export default function ChangePasswordForm({ userId, onSuccess }: ChangePasswordFormProps) {
  const [formData, setFormData] = useState({
    old_password: '',
    new_password: '',
    retype_password: ''
  });
  const [showPasswords, setShowPasswords] = useState({
    old: false,
    new: false,
    retype: false
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const t = useTranslations('password-form');

  const currentUser = authUtils.getUserData();
  const isOwnPassword = currentUser?.id === userId;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (isOwnPassword && !formData.old_password) {
      newErrors.old_password = t('currentPasswordRequired');
    }

    if (!formData.new_password) {
      newErrors.new_password = t('newPasswordRequired');
    } else if (formData.new_password.length < 8) {
      newErrors.new_password = t('passwordTooShort');
    } else if (!/(?=.*[a-z])(?=.*\d)/.test(formData.new_password)) {
      newErrors.new_password = t('passwordComplexity');
    }

    if (!formData.retype_password) {
      newErrors.retype_password = t('confirmPasswordRequired');
    } else if (formData.new_password !== formData.retype_password) {
      newErrors.retype_password = t('passwordsDoNotMatch');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setLoading(true);
    try {
      await userService.changePassword({
        user_id: userId,
        ...(isOwnPassword ? { old_password: formData.old_password } : {}),
        new_password: formData.new_password,
        retype_password: formData.retype_password
      });

      setFormData({ old_password: '', new_password: '', retype_password: '' });
      
      // If changing own password, show toast and log out
      if (isOwnPassword) {
        toast.success(t('passwordChangedSuccessfully') + '. ' + t('pleaseLoginAgain'));
        await authService.logout();
      } else {
        toast.success(t('passwordChangedSuccessfully'));
      }
      
      onSuccess?.();
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : 'Failed to change password';
      toast.error(message);
    } finally {
      setLoading(false);
    }
  };

  const togglePasswordVisibility = (field: 'old' | 'new' | 'retype') => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  return (
    <motion.form
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      onSubmit={handleSubmit}
      className="space-y-4 bg-white rounded-lg p-6 shadow-sm border border-gray-100 max-w-md"
    >
      <div className="flex items-center space-x-2 text-gray-700 mb-4">
        <FaKey className="w-4 h-4" />
        <h3 className="text-lg font-semibold">{t('changePassword')}</h3>
      </div>

      {isOwnPassword && (
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('currentPassword')}
          </label>
          <div className="relative">
            <input
              type={showPasswords.old ? 'text' : 'password'}
              value={formData.old_password}
              onChange={(e) => {
                setFormData(prev => ({ ...prev, old_password: e.target.value }));
                if (errors.old_password) {
                  setErrors(prev => ({ ...prev, old_password: '' }));
                }
              }}
              className={`block w-full pr-10 py-2 text-sm border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                errors.old_password ? 'border-red-300' : 'border-gray-300'
              }`}
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility('old')}
              className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-500"
            >
              {showPasswords.old ? <FaEyeSlash className="w-4 h-4" /> : <FaEye className="w-4 h-4" />}
            </button>
          </div>
          {errors.old_password && (
            <p className="mt-1 text-sm text-red-600">{errors.old_password}</p>
          )}
        </div>
      )}

      <div className="relative">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {t('newPassword')}
        </label>
        <div className="relative">
          <input
            type={showPasswords.new ? 'text' : 'password'}
            value={formData.new_password}
            onChange={(e) => {
              setFormData(prev => ({ ...prev, new_password: e.target.value }));
              if (errors.new_password) {
                setErrors(prev => ({ ...prev, new_password: '' }));
              }
            }}
            className={`block w-full pr-10 py-2 text-sm border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
              errors.new_password ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          <button
            type="button"
            onClick={() => togglePasswordVisibility('new')}
            className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-500"
          >
            {showPasswords.new ? <FaEyeSlash className="w-4 h-4" /> : <FaEye className="w-4 h-4" />}
          </button>
        </div>
        {errors.new_password && (
          <p className="mt-1 text-sm text-red-600">{errors.new_password}</p>
        )}
      </div>

      <div className="relative">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {t('confirmNewPassword')}
        </label>
        <div className="relative">
          <input
            type={showPasswords.retype ? 'text' : 'password'}
            value={formData.retype_password}
            onChange={(e) => {
              setFormData(prev => ({ ...prev, retype_password: e.target.value }));
              if (errors.retype_password) {
                setErrors(prev => ({ ...prev, retype_password: '' }));
              }
            }}
            className={`block w-full pr-10 py-2 text-sm border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
              errors.retype_password ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          <button
            type="button"
            onClick={() => togglePasswordVisibility('retype')}
            className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-500"
          >
            {showPasswords.retype ? <FaEyeSlash className="w-4 h-4" /> : <FaEye className="w-4 h-4" />}
          </button>
        </div>
        {errors.retype_password && (
          <p className="mt-1 text-sm text-red-600">{errors.retype_password}</p>
        )}
      </div>

      <motion.button
        whileTap={{ scale: 0.95 }}
        type="submit"
        disabled={loading}
        className={`w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
          loading ? 'opacity-75 cursor-not-allowed' : ''
        }`}
      >
        {loading ? (
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            <span>{t('changingPassword')}</span>
          </div>
        ) : (
          t('changePasswordButton')
        )}
      </motion.button>

      <div className="mt-2 text-xs text-gray-500 space-y-1">
        <p className="font-medium">{t('passwordRequirements')}</p>
        <div className="space-y-1">
          <div className="flex items-center space-x-1">
            <span>•</span>
            <span>{t('atLeast8Characters')}</span>
          </div>
          <div className="flex items-center space-x-1">
            <span>•</span>
            <span>{t('uppercaseLowercaseLetters')}</span>
          </div>
          <div className="flex items-center space-x-1">
            <span>•</span>
            <span>{t('atLeastOneNumber')}</span>
          </div>
        </div>
      </div>
    </motion.form>
  );
}
