"use client";

import { useTranslations } from 'next-intl';

export default function Footer() {
  const casinoName = process.env.NEXT_PUBLIC_CASINO_NAME || 'Casino Admin';
  const currentYear = new Date().getFullYear();
  const t = useTranslations('footerComponent');

  return (
    <footer className="bg-[var(--color-dark)] border-t border-[var(--color-mid)]/50 py-4">
      <div className="container mx-auto text-center text-sm text-[var(--color-light)]">
        {t('allRightsReserved')} {currentYear}  {casinoName}.
      </div>
    </footer>
  );
}
