import axios from 'axios';
import { API_BASE_URL } from '@/config';
import { authUtils } from '@/utils/auth.utils';
import { authService } from '@/services/auth.service';

const API_URL = `${API_BASE_URL}/api/v1/cashflow`;

export interface Transaction {
  id: number;
  fromUserId: number;
  toUserId: number;
  amount: string;
  type: 'add' | 'deduct';
  createdAt: string;
  updatedAt: string;
  fromUser: {
    id: number;
    username: string;
    role: string;
  };
  toUser: {
    id: number;
    username: string;
    role: string;
  };
}

export interface TransactionResponse {
  status: string;
  data: Transaction[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface TransactionParams {
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
  userId?: number;
}

// Create axios instance with interceptors
const apiClient = axios.create({
  baseURL: API_URL,
  withCredentials: true, // Important for cookies
});

// Add response interceptor for 401 errors
apiClient.interceptors.response.use(
  response => response,
  async error => {
    if (error.response?.status === 401) {
      try {
        // Try to refresh the token
        await authService.refreshToken();

        // Retry the original request
        return axios(error.config);
      } catch (refreshError) {
        // If refresh fails, clear auth and redirect
        console.log('redirect caused by a problem in the token. Please login again !', refreshError);
        authUtils.clearAuthData();
        window.location.href = '/';
      }
    }
    return Promise.reject(error);
  }
);

export const transactionService = {
  async getMyTransactions(params: TransactionParams): Promise<TransactionResponse> {
    // Ensure we have a valid token before making the request
    await authUtils.ensureValidToken();

    const response = await apiClient.get('/history/all', {
      params,
    });
    return response.data;
  },

  async getUserTransactions(userId: number, params: TransactionParams): Promise<TransactionResponse> {
    // Ensure we have a valid token before making the request
    await authUtils.ensureValidToken();

    const response = await apiClient.get(`/history/${userId}`, {
      params,
    });
    return response.data;
  },
};