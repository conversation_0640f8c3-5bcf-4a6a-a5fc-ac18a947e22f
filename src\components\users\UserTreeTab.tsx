'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { FaSync, FaSearch } from 'react-icons/fa';
import { toast } from 'sonner';
import { userService, type UserProfile } from '@/services/user.service';
import { FaCog } from 'react-icons/fa';
import { useTranslations } from 'next-intl';

interface UserTreeTabProps {
  userProfile: UserProfile;
  onManageUser?: (userId: number) => void;
}

interface TreeNode {
  id: number;
  username: string;
  role: string;
  balance: string;
  currency: string;
  is_banned: boolean;
  created_at: string;
  updated_at: string;
  children: TreeNode[];
}

export default function UserTreeTab({ userProfile, onManageUser }: UserTreeTabProps) {
  const [treeData, setTreeData] = useState<TreeNode | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [minBalance, setMinBalance] = useState('');
  const [bannedFilter, setBannedFilter] = useState('all');
  const t = useTranslations('UserTreeTab');

  const fetchTreeData = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await userService.getUserTree(userProfile.id);
      setTreeData(response.data);
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : t('failedToFetchTreeData');
      toast.error(message);
    } finally {
      setIsLoading(false);
    }
  }, [userProfile.id, t]);

  useEffect(() => {
    if (userProfile) {
      fetchTreeData();
    }
  }, [userProfile, fetchTreeData]);

  const filterTree = useCallback((node: TreeNode): TreeNode | null => {
    // Filter by search term
    const matchesSearch = node.username.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Filter by balance
    const matchesBalance = !minBalance || parseFloat(node.balance) >= parseFloat(minBalance);
    
    // Filter by banned status
    const matchesBanned = bannedFilter === 'all' || 
      (bannedFilter === 'banned' && node.is_banned) ||
      (bannedFilter === 'active' && !node.is_banned);
    
    // If current node matches, include it and filter children
    if (matchesSearch && matchesBalance && matchesBanned) {
      return {
        ...node,
        children: node.children
          .map(child => filterTree(child))
          .filter(child => child !== null) as TreeNode[]
      };
    }
    
    // If current node doesn't match but has matching children, include it
    const filteredChildren = node.children
      .map(child => filterTree(child))
      .filter(child => child !== null) as TreeNode[];
    
    if (filteredChildren.length > 0) {
      return {
        ...node,
        children: filteredChildren
      };
    }
    
    return null;
  }, [searchTerm, minBalance, bannedFilter]);

  const filteredTree = useMemo(() => {
    if (!treeData) return null;
    return filterTree(treeData);
  }, [treeData, filterTree]);

  const UserNode = ({ node, level = 0 }: { node: TreeNode; level?: number }) => {
    const [isExpanded, setIsExpanded] = useState(level < 1); // Auto-expand first 2 levels
    
    const roleColors: Record<string, string> = {
                      owner:  'bg-purple-100 text-purple-800',
                      superadmin:'bg-blue-500 text-white',
                      admin: 'bg-yellow-500 text-black',
                      cashier: 'bg-green-500 text-white',
                      player: 'bg-gray-500 text-white' ,
                      default: 'bg-gray-500 text-white'
    };

    return (
      <div className="pl-4">
        <div 
          className={`flex items-center gap-2 p-2 rounded-lg hover:bg-gray-50 cursor-pointer ${
            level === 0 ? 'bg-gray-100' : ''
          }`}
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex-1 flex items-center gap-2">
            {/* Expand/collapse icon */}
            {node.children.length > 0 && (
              <div className="w-4 h-4 flex items-center justify-center">
                {isExpanded ? '▼' : '▶'}
              </div>
            )}
            
            {/* User info */}
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="font-medium">{node.username}</span>
                <span className={`text-xs px-2 py-1 rounded-full ${roleColors[node.role] || roleColors.default}`}>
                  {node.role}
                </span>
                {node.is_banned && (
                  <span className="text-xs px-2 py-1 rounded-full bg-red-100 text-red-800">
                    {t('banned')}
                  </span>
                )}
              </div>
              <div className="text-sm text-gray-600">
                {t('balance')}: {node.balance} {node.currency}
              </div>
            </div>
            {onManageUser && (
              <button 
                onClick={(e) => {
                  e.stopPropagation();
                  onManageUser(node.id);
                }}
                className="text-gray-500 hover:text-blue-600 transition-colors p-1"
                title={t('manageUser')}
              >
                <FaCog className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {/* Children */}
        {isExpanded && node.children.length > 0 && (
          <div className="pl-6 border-l-2 border-gray-200">
            {node.children.map(child => (
              <UserNode key={child.id} node={child} level={level + 1} />
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="p-4">
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="p-6">
          <h3 className="text-lg font-semibold mb-4">{t('userTree')}</h3>
            
          {/* Search and Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="relative">
              <div className="relative">
                <FaSearch className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder={t('searchUsersPlaceholder')}
                  value={searchTerm}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
                  className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
              
            <div className="relative">
              <input
                type="number"
                placeholder={t('minBalance')}
                value={minBalance}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setMinBalance(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                min="0"
                step="0.01"
              />
            </div>
              
            <select
              value={bannedFilter}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setBannedFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">{t('allStatuses')}</option>
              <option value="banned">{t('bannedOnly')}</option>
              <option value="active">{t('activeOnly')}</option>
            </select>
          </div>
          
          {isLoading ? (
            <div className="flex justify-center py-4">
              <FaSync className="animate-spin h-5 w-5 text-gray-500" />
            </div>
          ) : filteredTree ? (
            <div className="space-y-2">
              <UserNode node={filteredTree} />
            </div>
          ) : (
            <div className="text-center text-gray-500">
              {t('noMatchingUsers')}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
