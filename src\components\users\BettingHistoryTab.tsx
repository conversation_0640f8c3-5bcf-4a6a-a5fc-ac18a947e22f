'use client';

import { useState, useEffect, useCallback } from 'react';
import { format } from 'date-fns';
import { FaSync } from 'react-icons/fa';
import { toast } from 'sonner';
import { GameService } from '@/services/game.service';
import { type UserProfile } from '@/services/user.service';
import { authUtils } from '@/utils/auth.utils';
import { useTranslations } from 'next-intl';

interface BettingHistoryTabProps {
  userProfile: UserProfile;
}

interface GameHistory {
  id: number;
  gameId: string;
  name: string;
  bet: number;
  win: number;
  action: string;
  date: Date;
  balance_before: number;
  balance_after: number;
  user: {
    id: number;
    username: string;
  };
}

export default function BettingHistoryTab({ userProfile }: BettingHistoryTabProps) {
  const [history, setHistory] = useState<GameHistory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 1
  });

  const t = useTranslations('BettingHistoryTab');

  const fetchHistory = useCallback(async () => {
    if (!userProfile) return;
    setIsLoading(true);
    try {
      const currentUser = authUtils.getUserData();
      if (!currentUser) {
        toast.error(t('userNotAuthenticated'));
        return;
      }
      const response = await GameService.getGameHistoryByUserId(currentUser.id, userProfile.id, {
        page: pagination.page,
        limit: pagination.limit,
        startDate: startDate || undefined,
        endDate: endDate || undefined,
      });

      setHistory(response.history);
      setPagination(prev => ({
        ...prev,
        total: response.total,
        totalPages: Math.ceil(response.total / prev.limit)
      }));
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : t('failedToFetchGameHistory');
      console.error('Error fetching game history:', error);
      toast.error(message);
    } finally {
      setIsLoading(false);
    }
  }, [pagination.page, pagination.limit, startDate, endDate, userProfile, t]);

  useEffect(() => {
    fetchHistory();
  }, [fetchHistory]);

  return (
    <div className="p-4">
      {/* Filters */}
      <div className="mb-6 flex flex-col sm:flex-row items-start sm:items-end gap-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full sm:w-auto">
          <label className="text-sm font-medium text-gray-700">{t('from')}:</label>
          <input
            type="date"
            value={startDate ? format(new Date(startDate), 'yyyy-MM-dd') : ''}
            onChange={(e) => {
              const selectedDate = e.target.value;
              const startDate = selectedDate ? `${selectedDate}T00:01` : '';
              setStartDate(startDate);
            }}
            className="input-field w-full sm:w-40"
          />
        </div>
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full sm:w-auto">
          <label className="text-sm font-medium text-gray-700">{t('to')}:</label>
          <input
            type="date"
            value={endDate ? format(new Date(endDate), 'yyyy-MM-dd') : ''}
            onChange={(e) => {
              const selectedDate = e.target.value;
              const endDate = selectedDate ? `${selectedDate}T23:59` : '';
              setEndDate(endDate);
            }}
            className="input-field w-full sm:w-40"
          />
        </div>
        <button
          onClick={() => {
            setStartDate('');
            setEndDate('');
          }}
          className="btn-primary px-3 py-1.5 text-sm w-full sm:w-auto"
          disabled={!startDate && !endDate}
        >
          {t('clear')}
        </button>
      </div>

      {/* Game History Table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('game')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('bet')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('win')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('action')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('date')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('balanceBefore')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('balanceAfter')}</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoading ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 whitespace-nowrap text-center">
                    <FaSync className="animate-spin h-5 w-5 mx-auto text-gray-500" />
                  </td>
                </tr>
              ) : history.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 whitespace-nowrap text-center text-gray-500">
                    {t('noGameHistoryFound')}
                  </td>
                </tr>
              ) : (
                history.map((item) => (
                  <tr key={item.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.bet}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.win}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.action}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {format(new Date(item.date), 'MMM dd, yyyy HH:mm')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.balance_before}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.balance_after}</td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="px-6 py-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-700">
                {t('showingPage')} <span className="font-medium">{pagination.page}</span> {t('ofText')}{' '}
                <span className="font-medium">{pagination.totalPages}</span>
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                  disabled={pagination.page === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                >
                  {t('previous')}
                </button>
                <button
                  onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.totalPages, prev.page + 1) }))}
                  disabled={pagination.page === pagination.totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                >
                  {t('next')}
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
