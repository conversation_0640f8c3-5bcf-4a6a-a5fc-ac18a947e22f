import { useState, useEffect } from 'react';
import { authUtils } from '@/utils/auth.utils';
import { toast } from 'sonner';
import { FaSync } from 'react-icons/fa';
import { useTranslations } from 'next-intl';

interface RefreshButtonProps {
  className?: string;
}

export const RefreshButton = ({ className }: RefreshButtonProps) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isOnCooldown, setIsOnCooldown] = useState(false);
  const t = useTranslations('refreshButtonUi');

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    if (isOnCooldown) {
      timeoutId = setTimeout(() => {
        setIsOnCooldown(false);
      }, 60000); // 1 minute cooldown
    }
    return () => clearTimeout(timeoutId);
  }, [isOnCooldown]);

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      await authUtils.handleBalanceRefresh();
      toast.success(t('balanceUpdated'));
    } catch (error) {
      console.error(t('BalanceRefreshfailed:'), error);
    } finally {
      setIsRefreshing(false);
      setIsOnCooldown(true);
    }
  };

  return (
    <button
      onClick={handleRefresh}
      disabled={isRefreshing || isOnCooldown}
      className={`p-1 rounded-full hover:bg-gray-200 transition-colors ${className}`}
    >
      <div className="relative">
        <FaSync 
          className={`w-4 h-4 text-[#00b894] transition-transform ${isRefreshing ? 'animate-spin' : ''}`}
        />
      </div>
    </button>
  );
};
