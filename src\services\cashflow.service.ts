import { authUtils } from '@/utils/auth.utils';
import { API_BASE_URL } from '@/config';
import { authService } from '@/services/auth.service';

const API_URL = `${API_BASE_URL}/api/v1/cashflow`;

interface CashFlowResponse {
  status: string;
  message: string;
}

interface CashFlowRequest {
  amount: number;
}

interface AddCashRequest extends CashFlowRequest {
  toUserId: number;
}

interface DeductCashRequest extends CashFlowRequest {
  fromUserId: number;
}

export const cashflowService = {
  /**
   * Adds cash to the selected user (receiver) from the logged-in user (sender)
   * @param params.toUserId The ID of the selected user (receiver)
   * @param params.amount The amount to add
   */
  async addCash(params: AddCashRequest): Promise<CashFlowResponse> {
    const fromUserId = authUtils.getUserData()?.id;
    try {
      // Ensure we have a valid token before making the request
      await authUtils.ensureValidToken();

      const response = await fetch(`${API_URL}/add`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': authUtils.getCsrfToken() || '',
        },
        credentials: 'include', // Important for cookies
        body: JSON.stringify({
          toUserId: params.toUserId,
          fromUserId,
          amount: params.amount,
        }),
      });

      // If we get a 401, try to refresh the token and retry
      if (response.status === 401) {
        try {
          await authService.refreshToken();

          // Retry the request with the new token
          const retryResponse = await fetch(`${API_URL}/add`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-Token': authUtils.getCsrfToken() || '',
            },
            credentials: 'include',
            body: JSON.stringify({
              toUserId: params.toUserId,
              fromUserId,
              amount: params.amount,
            }),
          });

          const retryData = await retryResponse.json();

          if (!retryResponse.ok || retryData.status === 'error') {
            throw new Error(retryData.message || 'Transaction failed');
          }

          return retryData;
        } catch (error) {
          console.log(error);
          authUtils.clearAuthData();
          window.location.href = '/';
          throw new Error('Session expired. Please login again.');
        }
      }

      const data = await response.json();

      // Check if the response indicates an error
      if (!response.ok || data.status === 'error') {
        throw new Error(data.message || 'Transaction failed');
      }

      return data;
    } catch (error: unknown) {
      throw error instanceof Error ? error : new Error('Transaction failed');
    }
  },

  async deductCash(params: DeductCashRequest): Promise<CashFlowResponse> {
    const toUserId = authUtils.getUserData()?.id;
    try {
      // Ensure we have a valid token before making the request
      await authUtils.ensureValidToken();

      const response = await fetch(`${API_URL}/deduct`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': authUtils.getCsrfToken() || '',
        },
        credentials: 'include', // Important for cookies
        body: JSON.stringify({
          fromUserId: params.fromUserId,
          toUserId,
          amount: params.amount,
        }),
      });

      // If we get a 401, try to refresh the token and retry
      if (response.status === 401) {
        try {
          await authService.refreshToken();

          // Retry the request with the new token
          const retryResponse = await fetch(`${API_URL}/deduct`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-Token': authUtils.getCsrfToken() || '',
            },
            credentials: 'include',
            body: JSON.stringify({
              fromUserId: params.fromUserId,
              toUserId,
              amount: params.amount,
            }),
          });

          const retryData = await retryResponse.json();

          if (!retryResponse.ok || retryData.status === 'error') {
            throw new Error(retryData.message || 'Transaction failed');
          }

          return retryData;
        } catch (error) {
          authUtils.clearAuthData();
          window.location.href = '/';
          console.log(error);
          throw new Error('Session expired. Please login again.');
        }
      }

      const data = await response.json();

      // Check if the response indicates an error
      if (!response.ok || data.status === 'error') {
        throw new Error(data.message || 'Transaction failed');
      }

      return data;
    } catch (error: unknown) {
      throw error instanceof Error ? error : new Error('Transaction failed');
    }
  }

};
