'use client';

import { useState, useEffect, useCallback } from 'react';
import { format } from 'date-fns';
import { FaSync } from 'react-icons/fa';
import { toast } from 'sonner';
import { transactionService, type Transaction } from '@/services/transaction.service';
import { type UserProfile } from '@/services/user.service';
import { useTranslations } from 'next-intl';

interface TransactionHistoryTabProps {
  userProfile: UserProfile;
}

export default function TransactionHistoryTab({ userProfile }: TransactionHistoryTabProps) {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 1,
  });

  const t = useTranslations('TransactionHistoryTab');

  const fetchTransactions = useCallback(async () => {
    if (!userProfile) return;
    setIsLoading(true);
    try {
      const response = await transactionService.getUserTransactions(userProfile.id, {
        page: pagination.page,
        limit: pagination.limit,
        startDate: startDate || undefined,
        endDate: endDate || undefined,
      });
      setTransactions(response.data);
      setPagination(response.pagination);
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : t('failedToFetchTransactions');
      toast.error(message);
    } finally {
      setIsLoading(false);
    }
  }, [userProfile, pagination.page, pagination.limit, startDate, endDate, t]);

  useEffect(() => {
    if (userProfile) {
      fetchTransactions();
    }
  }, [userProfile, fetchTransactions, pagination.page, startDate, endDate]);

  return (
    <div className="p-4">
      {/* Filters */}
      <div className="mb-6 flex flex-col sm:flex-row items-start sm:items-end gap-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full sm:w-auto">
          <label className="text-sm font-medium text-gray-700">{t('from')}:</label>
          <input
            type="date"
            value={startDate ? format(new Date(startDate), 'yyyy-MM-dd') : ''}
            onChange={(e) => {
              const selectedDate = e.target.value;
              const startDate = selectedDate ? `${selectedDate}T00:01` : '';
              setStartDate(startDate);
            }}
            className="input-field w-full sm:w-40"
          />
        </div>
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full sm:w-auto">
          <label className="text-sm font-medium text-gray-700">{t('to')}:</label>
          <input
            type="date"
            value={endDate ? format(new Date(endDate), 'yyyy-MM-dd') : ''}
            onChange={(e) => {
              const selectedDate = e.target.value;
              const endDate = selectedDate ? `${selectedDate}T23:59` : '';
              setEndDate(endDate);
            }}
            className="input-field w-full sm:w-40"
          />
        </div>
        <button
          onClick={() => {
            setStartDate('');
            setEndDate('');
          }}
          className="btn-primary px-3 py-1.5 text-sm w-full sm:w-auto"
          disabled={!startDate && !endDate}
        >
          {t('clear')}
        </button>
      </div>

      {/* Transactions Table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('type')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('amount')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('from')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('to')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('date')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoading ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 whitespace-nowrap text-center">
                    <FaSync className="animate-spin h-5 w-5 mx-auto text-gray-500" />
                  </td>
                </tr>
              ) : transactions.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 whitespace-nowrap text-center text-gray-500">
                    {t('noTransactionsFound')}
                  </td>
                </tr>
              ) : (
                transactions.map((transaction) => (
                  <tr key={transaction.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center gap-1 font-medium ${
                        transaction.type === 'add' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.type === 'add' ? '+' : '-'}
                        {transaction.type}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`flex items-center ${transaction.type === 'add' ? 'text-green-600' : 'text-red-600'}`}>
                        <span className='mr-1'>{transaction.amount}</span>
                        {new Intl.NumberFormat('en-US', {
                          style: 'currency',
                          currency: 'TND',}).format(0).replace(/[\d.,]+/g, '')}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col">
                        <span className="font-medium">{transaction.fromUser.username}</span>
                        <span
              className={`px-2 text-xs leading-5 font-semibold rounded-full  max-w-max  
                ${transaction.fromUser.role === 'owner' ? 'bg-purple-100 text-purple-800' : 
                  transaction.fromUser.role === 'superadmin' ? 'bg-blue-500 text-white' :
                  transaction.fromUser.role === 'admin' ? 'bg-yellow-500 text-black' :
                transaction.fromUser.role === 'cashier' ? 'bg-green-500 text-white' :
                transaction.fromUser.role === 'player' ? 'bg-gray-500 text-white' : 
                'bg-gray-500 text-white max-w-max'}`}
            >
              {transaction.fromUser.role.charAt(0).toUpperCase() + transaction.fromUser.role.slice(1)}
            </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col">
                        <span className="font-medium">{transaction.toUser.username}</span>
                        <span
              className={`px-2 text-xs leading-5 font-semibold rounded-full  max-w-max  
                ${transaction.toUser.role === 'owner' ? 'bg-purple-100 text-purple-800' : 
                  transaction.toUser.role === 'superadmin' ? 'bg-blue-500 text-white' :
                  transaction.toUser.role === 'admin' ? 'bg-yellow-500 text-black' :
                  transaction.toUser.role === 'cashier' ? 'bg-green-500 text-white' :
                  transaction.toUser.role === 'player' ? 'bg-gray-500 text-white' : 
                'bg-gray-500 text-white max-w-max'}`}
            >
              {transaction.toUser.role.charAt(0).toUpperCase() + transaction.toUser.role.slice(1)}
            </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {format(new Date(transaction.createdAt || new Date().toISOString()), 'dd/MM/yyyy HH:mm')}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="px-6 py-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-700">
                {t('showingPage')} <span className="font-medium">{pagination.page}</span> {t('ofText')}{' '}
                <span className="font-medium">{pagination.totalPages}</span>
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                  disabled={pagination.page === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                >
                  {t('previous')}
                </button>
                <button
                  onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.totalPages, prev.page + 1) }))}
                  disabled={pagination.page === pagination.totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                >
                  {t('next')}
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
