import { getRequestConfig } from 'next-intl/server';
import { routing, type Locale } from '@/i18n/routing';

export default getRequestConfig(async ({ requestLocale }) => {
  // This typically corresponds to the `[locale]` segment
  let locale = await requestLocale;

  // Ensure that the incoming locale is valid
  if (!locale || !routing.locales.includes(locale as Locale)) {
    locale = routing.defaultLocale;
  }

  return {
    messages: (await import(`../messages/${locale}.json`)).default,
    locale: locale
  };
});
