"use client";

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { FaU<PERSON>, <PERSON>aLock, FaChevronRight } from 'react-icons/fa';
import { useRouter } from 'next/navigation';
import { authService } from '@/services/auth.service';
import { authUtils } from '@/utils/auth.utils';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';
import LanguageSwitcher from '@/components/LanguageSwitcher';

export default function LoginPage() {
  const t = useTranslations('Login');
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    rememberMe: false
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  // Check if user is already authenticated
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check if we have user data
        const userData = authUtils.getUserData();
        if (userData) {
          // If user data exists, redirect to dashboard without token refresh
          // The middleware and AuthProvider will handle token validation
          router.push('/dashboard');
        }
      } catch (error) {
        console.error('Auth check failed:', error);
      }
    };

    // Check URL for error messages - only on client side
    if (typeof window !== 'undefined') {
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const errorMessage = urlParams.get('error');
        if (errorMessage) {
          const decodedMessage = decodeURIComponent(errorMessage);
          if (decodedMessage) {
            toast.error(decodedMessage);
            // Clear the error from URL
            window.history.replaceState(null, '', window.location.pathname);
          }
        }
      } catch (error) {
        console.error('Error handling URL error message:', error);
      }
    }

    checkAuth();
  }, [router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const response = await authService.login(formData.username, formData.password);

      if (response.status === 'success' && response.data) {
        const { user } = response.data;

        if (user.role === 'player') {
          authUtils.clearAuthData();
          setError(t('playersNotAllowed'));
          return;
        }

        try {
          // Store user data only (tokens are in HTTP-only cookies)
          authUtils.setUserData({
            ...user,
            balance: user.balance.toString()
          });

          // Mark that we just logged in successfully
          authUtils.setLastTokenRefresh(Date.now());

          // Clear sensitive form data
          setFormData({ username: '', password: '', rememberMe: false });

          // Force navigation to dashboard
          window.location.href = '/dashboard';
        } catch (error) {
          console.error('Error storing user data:', error);
          setError(t('failedToStoreAuthData'));
          authUtils.clearAuthData();
        }
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (err: unknown) {
      let errorMessage = 'Failed to login. Please try again.';

      // Handle specific error cases
      if (typeof err === 'object' && err !== null) {
        const errorObj = err as {
          response?: {
            status?: number;
            data?: { message?: string };
          };
          message?: string;
        };

        if (errorObj.response) {
          if (errorObj.response.status === 401) {
            errorMessage = t('invalidUsernameOrPassword');
          } else if (errorObj.response.data?.message) {
            errorMessage = errorObj.response.data.message;
          }
        } else if (errorObj.message) {
          errorMessage = errorObj.message;
        }
      }

      setError(errorMessage);
      // Clear password field on error
      setFormData(prev => ({ ...prev, password: '' }));
      // Show error toast with more user-friendly message
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-page min-h-screen w-full flex bg-[var(--bg-primary)]">
      {/* Left Side - Welcome Section */}
      <div className="hidden lg:flex flex-col justify-center p-12 w-1/2 bg-[var(--bg-secondary)]">
        <div className="max-w-md space-y-6">
          <h1 className="text-5xl font-bold text-[var(--text-dark)]">
            {t('welcome')}
          </h1>
          <p className="text-xl text-[var(--text-light)]">
            {t('subtitle')}
          </p>
        </div>
      </div>

      {/* Right Side - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-[var(--login-right-bg)]">
        <div className="w-full max-w-md space-y-8">
          <div className="absolute top-8 right-4">
            <div className="mt-2 -mt-4"> {/* Added div for positioning and negative margin-top */}
              <LanguageSwitcher isLoginPage={true} />
            </div>
          </div>
          {/* Login Form */}
          <form onSubmit={handleLogin} className="bg-[var(--bg-secondary)] p-8 space-y-6 rounded-xl shadow-lg" autoComplete="off">
            <div className="text-center space-y-2">
            <Image
            className="mx-auto"
            src="/ibetx.svg"
            alt="iBetx Logo"
            height={142}
            width={122}
            priority
            style={{ height: 58, width: 150 }} />
              <p className="text-[var(--text-light)]">{t('signIn')}</p>
            </div>
          {error && (
            <div className="p-3 text-sm text-red-500 bg-red-100 rounded-lg" role="alert">
              {error}
            </div>
          )}

          <div className="space-y-6">
            {/* Username Input */}
            <div className="space-y-2">
              <label htmlFor="username" className="block text-sm font-medium text-[var(--text-light)]">
                {t('username')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center z-10">
                  <FaUser className="h-5 w-5 text-blue-600" />
                </div>
                <input
                  id="username"
                  name="username"
                  type="text"
                  value={formData.username}
                  onChange={handleInputChange}
                  required
                  autoComplete="username"
                  spellCheck="false"
                  className="block w-full pl-10 pr-4 py-2.5 text-[var(--text-dark)] rounded-lg border border-gray-700
                           focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                           bg-[var(--bg-primary)] transition-all duration-200"
                  placeholder={t('enterUsername')}
                />
              </div>
            </div>

            {/* Password Input */}
            <div className="space-y-2">
              <label htmlFor="password" className="block text-sm font-medium text-[var(--text-light)]">
                {t('password')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center z-10">
                  <FaLock className="h-5 w-5 text-blue-600" />
                </div>
                <input
                  id="password"
                  name="password"
                  type="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  autoComplete="current-password"
                  className="block w-full pl-10 pr-4 py-2.5 text-[var(--text-dark)] rounded-lg border border-gray-700
                           focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                           bg-[var(--bg-primary)] transition-all duration-200"
                  placeholder={t('enterPassword')}
                />
              </div>
            </div>

            {/* Remember Me */}
            <div className="flex items-center">
              <input
                id="rememberMe"
                name="rememberMe"
                type="checkbox"
                checked={formData.rememberMe}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded
                         cursor-pointer transition-colors duration-200"
              />
              <label htmlFor="rememberMe" className="ml-2 block text-sm text-[var(--text-light)] cursor-pointer">
                {t('rememberMe')}
              </label>
            </div>
          </div>

          {/* Sign In Button */}
          <button
            type="submit"
            disabled={loading}
            className="group relative w-full flex justify-center py-3 px-4 border border-transparent
                     text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700
                     focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
                     transition-all duration-200 hover-lift disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span className="absolute inset-y-0 right-0 flex items-center pr-3">
              <FaChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
            </span>
            {loading ? t('signingIn') : t('signInButton')}
          </button>
        </form>

        {/* Footer */}
        <p className="text-center text-sm text-[var(--text-light)]">
          {t('noAccount')}{' '}
          <a href="#" className="font-medium text-blue-400 hover:text-blue-300 transition-colors duration-200">
            {t('contactAdmin')}
          </a>
        </p>
      </div>
    </div>
    </div>
  );
}
