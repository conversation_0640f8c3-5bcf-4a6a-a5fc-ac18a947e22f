"use client";

import { useState, useEffect } from 'react';
import { Fa<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaEyeSlash, FaCheckCircle } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { authUtils } from '@/utils/auth.utils';
import { toast } from 'sonner';
import { API_BASE_URL } from '@/config';
import { useRouter } from 'next/navigation';
import dynamic from 'next/dynamic';
import { useTranslations } from 'next-intl';

const MotionDiv = dynamic(() => import('framer-motion').then((mod) => mod.motion.div), { ssr: false });

enum UserRole {
  SUPEROWNER = 'superowner',
  OWNER = 'owner',
  SUPERADMIN = 'superadmin',
  ADMIN = 'admin',
  CASHIER = 'cashier',
  PLAYER = 'player'
}

interface FormData {
  username: string;
  password: string;
  role: string;
  currency?: string;
}

export default function CreateUserPage() {
  const [formData, setFormData] = useState<FormData>({
    username: '',
    role: '',
    password: 'password123',
    currency: 'TND'
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const currentUser = authUtils.getUserData();
  const router = useRouter();
  const t = useTranslations('createUserPage');

  // Get available roles based on current user's role
  const getAvailableRoles = () => {
    switch (currentUser?.role) {
      case UserRole.SUPEROWNER:
        return [
          { id: 'owner', label: 'Owner', value: 'owner', color: 'bg-purple-100 text-purple-800' },
          { id: 'superadmin', label: 'Superadmin', value: 'superadmin', color: 'bg-purple-100 text-purple-800' }
        ];
      case UserRole.OWNER:
        return [
          { id: 'superadmin', label: 'Superadmin', value: 'superadmin', color: 'bg-purple-100 text-purple-800' }
        ];
      case UserRole.SUPERADMIN:
        return [
          { id: 'admin', label: 'Admin', value: 'admin', color: 'bg-purple-100 text-purple-800' }
        ];
      case UserRole.ADMIN:
        return [
          { id: 'cashier', label: 'Cashier', value: 'cashier', color: 'bg-blue-100 text-blue-800' }
        ];
      case UserRole.CASHIER:
        return [
          { id: 'player', label: 'Player', value: 'player', color: 'bg-green-100 text-green-800' }
        ];
      default:
        return [];
    }
  };

  const validateForm = (): string | null => {
    if (formData.username.length < 5 || formData.username.length > 20) {
      return t('usernameLengthError');
    }
    if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      return t('usernameFormatError');
    }
    if (formData.password.length < 8) {
      return t('passwordLengthError');
    }
    if (!/(?=.*[a-z])(?=.*\d)/.test(formData.password)) {
      return t('passwordComplexityError');
    }
    if (!formData.role) {
      return t('roleRequiredError');
    }
    if (formData.currency && formData.currency.length !== 3) {
      return t('currencyLengthError');
    }
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      toast.error(validationError);
      return;
    }

    setIsLoading(true);
    const currentRole = formData.role; // Store current role before reset

    try {
      const response = await fetch(`${API_BASE_URL}/api/users/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': authUtils.getCsrfToken() || ''
        },
        credentials: 'include', // Important for cookies
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to create user');
      }

      // Reset form before showing toast
      setFormData({
        username: '',
        password: 'password123',
        role: '',
        currency: 'TND'
      });

      // Show success message after state update
      setTimeout(() => {
        toast.success(
          <div className="flex items-center gap-2">
            <FaCheckCircle className="text-green-500" />
            <div>
              <p className="font-medium">{t('userCreatedSuccessfully')}</p>
              <p className="text-sm text-gray-500">
                {t('newAccountCreated', { role: currentRole })}
              </p>
            </div>
          </div>
        );
      }, 0);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      setTimeout(() => {
        toast.error(errorMessage);
      }, 0);
    } finally {
      setIsLoading(false);
    }
  };

  // Redirect if user doesn't have permission to create users
  useEffect(() => {
    if (!currentUser || ![UserRole.OWNER, UserRole.SUPERADMIN, UserRole.CASHIER, UserRole.SUPEROWNER, UserRole.ADMIN].includes(currentUser.role as UserRole)) {
      router.replace('/dashboard');
    }
  }, [currentUser, router]);

  const availableRoles = getAvailableRoles();

  return (
    <div className="space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-2xl md:text-3xl font-bold gradient-text">{t('title')}</h1>
        <p className="text-gray-600">{t('description')}</p>
      </div>

      <div className="w-full max-w-[95%] bg-white rounded-lg shadow-lg hover:shadow-2xl transition-shadow duration-300 p-6">
        {/* Main Content */}
        <MotionDiv
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          {/* Form Card */}
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Username Field */}
              <div className="space-y-2">
                <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                  {t('usernameLabel')}
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaUser className="h-5 w-5 text-blue-600" />
                  </div>
                  <input
                    type="text"
                    id="username"
                    value={formData.username}
                    onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                    className="block w-full pl-10 pr-4 py-2.5 text-gray-900 rounded-lg border border-gray-300
                             focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                             bg-white transition-all duration-200"
                    placeholder={t('usernamePlaceholder')}
                    pattern="^[a-zA-Z0-9_]+$"
                    minLength={5}
                    maxLength={20}
                    required
                  />
                </div>
                <p className="text-xs text-gray-500">{t('usernameRequirements')}</p>
              </div>

              {/* Role Selection */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  {t('roleLabel')}
                </label>
                <div className="relative mt-2">
                  <div className="grid grid-cols-1 gap-2">
                    {availableRoles.map((role) => (
                      <label
                        key={role.id}
                        className={`relative flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all duration-200
                                  ${formData.role === role.value
                            ? 'border-blue-500 ' + role.color
                            : 'border-gray-200 hover:border-blue-300'
                          }`}
                      >
                        <input
                          type="radio"
                          name="role"
                          value={role.value}
                          checked={formData.role === role.value}
                          onChange={() => setFormData({ ...formData, role: role.value })}
                          className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                        />
                        <div className="ml-3 flex flex-col">
                          <span className="text-sm font-medium">
                            {role.label}
                          </span>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  {t('passwordLabel')}
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaLock className="h-5 w-5 text-blue-600" />
                  </div>
                  <input
                    type={showPassword ? 'text' : 'password'}
                    id="password"
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    className="block w-full pl-10 pr-12 py-2.5 text-gray-900 rounded-lg border border-gray-300
                             focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                             bg-white transition-all duration-200"
                    minLength={8}
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showPassword ? (
                      <FaEyeSlash className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    ) : (
                      <FaEye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    )}
                  </button>
                </div>
                <div className="mt-2 text-xs text-gray-500 space-y-1">
                  <p className="font-medium">{t('passwordRequirements')}</p>
                  <div className="space-y-1">
                    <div className="flex items-center space-x-1">
                      <span>•</span>
                      <span>{t('atLeast8Characters')}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span>•</span>
                      <span>{t('uppercaseLowercaseLetters')}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span>•</span>
                      <span>{t('atLeastOneNumber')}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="text-red-500 text-sm">
                  {error}
                </div>
              )}

              {/* Submit Button */}
              <motion.button
                type="submit"
                disabled={isLoading || !formData.username || !formData.role}
                className="w-full py-3 px-4 flex items-center justify-center space-x-2
                         text-white bg-gradient-to-r from-blue-600 to-blue-700
                         hover:from-blue-700 hover:to-blue-800
                         rounded-lg shadow-lg hover:shadow-xl
                         disabled:opacity-50 disabled:cursor-not-allowed
                         transition-all duration-200 transform hover:-translate-y-0.5"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>{t('creating')}...</span>
                  </>
                ) : (
                  <span>{t('createUserButton')}</span>
                )}
              </motion.button>
            </form>
        </MotionDiv>
      </div>
    </div>
  );
}
