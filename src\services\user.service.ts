import axios from 'axios';
import { authUtils } from '@/utils/auth.utils';
import { Transaction, BannedPlayer, UserSummary } from '@/types/dashboard';
import { API_BASE_URL } from '@/config';
import { authService } from '@/services/auth.service';

const API_URL =  `${API_BASE_URL}/api/users`;

// Create axios instance with interceptors
const apiClient = axios.create({
  baseURL: API_URL,
  withCredentials: true, // Important for cookies
});

// Add request interceptor to include CSRF token
apiClient.interceptors.request.use(
  async config => {
    // Skip for GET requests
    if (config.method?.toLowerCase() === 'get') {
      return config;
    }

    // For non-GET requests, ensure we have a valid token
    await authUtils.ensureValidToken();

    // Add CSRF token to headers for non-GET requests
    const csrfToken = authUtils.getCsrfToken();
    if (csrfToken) {
      config.headers['X-CSRF-Token'] = csrfToken;
    }

    return config;
  },
  error => Promise.reject(error)
);

// Add response interceptor for 401 errors
apiClient.interceptors.response.use(
  response => response,
  async error => {
    if (error.response?.status === 401) {
      try {
        // Try to refresh the token
        await authService.refreshToken();

        // Retry the original request
        const newConfig = { ...error.config };

        // Update CSRF token for the retry
        if (newConfig.method?.toLowerCase() !== 'get') {
          const csrfToken = authUtils.getCsrfToken();
          if (csrfToken) {
            newConfig.headers['X-CSRF-Token'] = csrfToken;
          }
        }

        return axios(newConfig);
      } catch (refreshToken) {
        // If refresh fails, clear auth and redirect
        console.log('redirect caused by a problem in the token. Please login again !', refreshToken);
        authUtils.clearAuthData();
        window.location.href = '/';
      }
    }
    return Promise.reject(error);
  }
);

export interface User {
  id: number;
  username: string;
  role: string;
  balance: string;
  currency: string;
  created_at?: string;
  is_banned?: boolean;
}

export interface PaginationInfo {
  total: number;
  page: number;
  totalPages: number;
  limit: number;
}

export interface SearchResponse {
  status: string;
  data: {
    users: User[];
    pagination: PaginationInfo;
  };
}

interface SearchParams {
  username?: string;
  page?: number;
  limit?: number;
  roles?: string;
  includeDescendants?: boolean;
  isBanned?: boolean;
  startDate?: string;
  endDate?: string;
  level?: number;
}

export interface UserProfile {
  id: number;
  username: string;
  role: string;
  balance: string;
  currency: string;
  is_active: boolean;
  is_banned: boolean;
  last_login: string;
  parent_id: number;
  createdAt: string;
  childCount?: number;
  parent_username?: string;
}

interface ChangePasswordParams {
  user_id: number;
  old_password?: string;
  new_password: string;
  retype_password: string;
}

export interface UserTree {
  id: number;
  username: string;
  role: string;
  balance: string;
  currency: string;
  is_banned: boolean;
  created_at: string;
  updated_at: string;
  children: UserTree[];
}

export interface DashboardStats {
  totalUsers: number;
  owner: number;
  superadmin: number;
  admin: number;
  cashier: number;
  player: number;
  banned: number;
  active: number;
  userBalance: string;
  totalGames: number;
  lastTransactions: Transaction[];
  lastBannedPlayers: BannedPlayer[];
  topOwners: UserSummary[];
  topSuperadmins: UserSummary[];
  topAdmins: UserSummary[];
  topCashiers: UserSummary[];
  topPlayers: UserSummary[];
  todayTransactions?: {
    sold: number;
    bought: number;
    took: number;
  };
  currency?: string;
}

export const userService = {
  async getDashboardStats(): Promise<{ status: string; data: DashboardStats }> {
    const response = await apiClient.get('/stats');
    return response.data;
  },
  /**
   * Search users with pagination and filters
   * @param params Search parameters
   */
  async searchUsers(params: SearchParams): Promise<SearchResponse> {
    try {
        const queryParams = new URLSearchParams();

        if (params.username && params.username.length >= 4) {
            queryParams.append('username', params.username);
        }
        if (!params.includeDescendants && params.roles) {
          queryParams.append('roles', params.roles);
        }
        if (params.includeDescendants) {
          queryParams.append('descendants', 'true');
        }
        if (params.page) {
            queryParams.append('page', params.page.toString());
        }
        if (params.limit) {
            queryParams.append('limit', params.limit.toString());
        }
        if (params.isBanned !== undefined) {
            queryParams.append('isBanned', params.isBanned.toString());
        }
        if (params.startDate) {
            queryParams.append('startDate', params.startDate);
        }
        if (params.endDate) {
            queryParams.append('endDate', params.endDate);
        }
        if (params.level) {
          queryParams.append('level', params.level.toString());
        }
        const response = await apiClient.get(`/search?${queryParams.toString()}`);

        return response.data;
    } catch (error: unknown) {
        if (axios.isAxiosError(error) &&
            (error.response?.status === 401 ||
                error.response?.data?.message?.includes('Token has been invalidated'))) {
            authUtils.clearAuthData();
            window.location.href = '/';
        }
        throw error;
    }
},
  /**
   * Ban a user and their descendants
   * @param userId The ID of the user to ban
   */
  async banUser(userId: number): Promise<{ status: string; message: string; data: { bannedUsers: number[] } }> {
    const response = await apiClient.post('/ban', { user_id: userId });
    return response.data;
  },

  /**
   * Unban a user and their descendants
   * @param userId The ID of the user to unban
   */
  async unbanUser(userId: number): Promise<{ status: string; message: string; data: { unbannedUsers: number[] } }> {
    const response = await apiClient.post('/unban', { user_id: userId });
    return response.data;
  },

  /**
   * Get user profile details
   * @param userId ID of the user to get details for
   */
  async getUserTree(userId: number): Promise<{ status: string; data: UserTree }> {
    const response = await apiClient.post('/tree', { user_id: userId });
    return response.data;
  },

  async getUserProfile(userId: number): Promise<UserProfile> {
    const response = await apiClient.post('/details', { user_id: userId });
    return response.data.data;
  },

  /**
   * Change user password with role-based access
   * @param params Password change parameters
   */
  async changePassword(params: ChangePasswordParams) {
    const response = await apiClient.post('/password', params);
    return response.data;
  },
};
