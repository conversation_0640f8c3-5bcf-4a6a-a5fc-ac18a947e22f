"use client";

import React, { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FaUserCircle, FaUsers, FaGamepad, FaTachometerAlt, FaChevronDown, FaExchangeAlt } from 'react-icons/fa';
import { useUserDetails } from '@/hooks/useUserDetails';
import Image from 'next/image';
import { useLocale, useTranslations } from 'next-intl';
import LanguageSwitcher from '@/components/LanguageSwitcher';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

interface MenuItem {
  title: string;
  icon: React.ReactNode;
  href?: string;
  submenu?: { title: string; href: string }[];
}

const Sidebar = ({ isOpen, onClose }: SidebarProps) => {
  const pathname = usePathname();
  const [expandedMenu, setExpandedMenu] = useState<string | null>(null);
  const { userDetails } = useUserDetails();
  const locale = useLocale();
  const t = useTranslations('sidebarMenu');


  const menuItems = useMemo((): MenuItem[] => [
    {
      title: t('dashboard'),
      icon: <FaTachometerAlt className="w-5 h-5" />,
      href: '/dashboard',
    },
    {
      title: t('users'),
      icon: <FaUsers className="w-5 h-5" />,
      submenu: [
        { title: t('createUser'), href: '/users/create' },
        { title: t('manageUserCash'), href: '/users/manage-cash' },
        { title: t('banUnbanUsers'), href: '/users/ban-management' },
        { title: t('allUsers'), href: '/users/search' },
      ],
    },
    {
      title: t('transactions'),
      icon: <FaExchangeAlt className="w-5 h-5" />,
      submenu: [
        { title: t('myTransactions'), href: '/transactions/my' },
        { title: t('userTransactions'), href: '/transactions/users' },
      ],
    },
    {
      title: t('games'),
      icon: <FaGamepad className="w-5 h-5" />,
      submenu: [
        { title: t('gameList'), href: '/games/list' },
        { title: t('bettingHistory'), href: '/games/betting-history' },
      ],
    },
  ], [t]);

  const filteredMenuItems = menuItems.map(item => {
    if (item.title === t('games') && userDetails?.role !== 'superowner') {
      return {
        ...item,
        submenu: item.submenu?.filter(subItem => subItem.title !== t('gameList'))
      };
    }
    return item;
  });

  const isSubmenuActive = (submenu: { href: string }[]) => {
    return submenu.some(item => {
      const hrefWithoutLocale = item.href.startsWith(`/${locale}`) ? item.href.slice(`/${locale}`.length) : item.href;
      return pathname === item.href || pathname === `/${locale}${hrefWithoutLocale}`;
    });
  };

  const toggleSubmenu = (title: string) => {
    setExpandedMenu(expandedMenu === title ? null : title);
  };

  const isActive = (href?: string) => {
    if (!href) return false;
    const hrefWithoutLocale = href.startsWith(`/${locale}`) ? href.slice(`/${locale}`.length) : href;
    return pathname === href || pathname === `/${locale}${hrefWithoutLocale}`;
  };

  useEffect(() => {
    const getActiveParentMenu = () => {
      return menuItems.find(item => 
        item.submenu?.some(subItem => {
          const hrefWithoutLocale = subItem.href.startsWith(`/${locale}`) ? subItem.href.slice(`/${locale}`.length) : subItem.href;
          return pathname === subItem.href || pathname === `/${locale}${hrefWithoutLocale}`;
        })
      )?.title || null;
    };

    const activeParent = getActiveParentMenu();
    if (activeParent) {
      setExpandedMenu(activeParent);
    }
  }, [pathname, menuItems, locale]);

  return (
    <>
      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`fixed top-0 left-0 z-50 h-full w-64 bg-[var(--color-dark)] border-r border-[var(--color-dark)] transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
        }`}
        style={{
          boxShadow: 'inset -1px 0 20px rgba(103, 132, 187, 0.1)',
          backgroundImage: `
            radial-gradient(circle at 20% 10%, rgba(103, 132, 187, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 90%, rgba(103, 132, 187, 0.1) 0%, transparent 50%)
          `
        }}
      >
        <div className="flex flex-col h-full">
          <div className="pt-2 pb-6 pl-6 pr-6">
            <Image
                        className="mx-auto"
                        src="/ibetx.svg"     
                        alt="iBetx Logo"
                        height={142}
                        width={122}
                        priority
                        style={{ height: 58, width: 150 }} />
          </div>
          
          <nav className="flex-1 overflow-y-auto px-4 py-2">
            {filteredMenuItems.map((item, index) => (
              <div key={index} className="mb-2 slide-in" style={{ animationDelay: `${index * 50}ms` }}>
                {item.submenu ? (
                  <>
                    <div
                      className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                        isSubmenuActive(item.submenu)
                          ? 'bg-[var(--color-mid)] text-white shadow-md border border-[var(--color-mid)]'
                          : 'text-[var(--color-light)] hover:bg-[var(--color-mid)]/50 hover:text-white'
                      }`}
                      onClick={() => toggleSubmenu(item.title)}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`transition-colors duration-200 ${
                          isSubmenuActive(item.submenu)
                            ? 'text-white'
                            : 'text-gray-400'
                        }`}>
                          {item.icon}
                        </div>
                        <span>{item.title}</span>
                      </div>
                      <div className={`transition-transform duration-200 ${
                        expandedMenu === item.title ? 'rotate-180' : ''
                      }`}>
                        <FaChevronDown className="w-4 h-4" />
                      </div>
                    </div>
                    <div className={`mt-2 ml-4 space-y-1 transition-all duration-200 ${
                      expandedMenu === item.title ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0 overflow-hidden'
                    }`}>
                      {item.submenu.map((subItem, subIndex) => (
                        <Link
                          key={subIndex}
                          href={subItem.href}
                          onClick={() => {
                            if (window.innerWidth < 1024) {
                              onClose();
                            }
                          }}
                          className={`flex items-center p-3 rounded-lg text-sm transition-all duration-200 ${
                            isActive(subItem.href)
                              ? 'bg-[var(--color-mid)] text-white border-l-2 border-white'
                              : 'text-[var(--color-light)] hover:bg-[var(--color-mid)]/50 hover:text-white'
                          }`}
                        >
                          <span className="ml-2">{subItem.title}</span>
                        </Link>
                      ))}
                    </div>
                  </>
                ) : (
                  <Link
                    href={item.href!}
                    onClick={() => {
                      if (window.innerWidth < 1024) {
                        onClose();
                      }
                    }}
                    className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${
                      isActive(item.href)
                        ? 'bg-[#1a1b26] text-white shadow-md border border-[#2a2b3a]'
                        : 'text-[#a1a1aa] hover:bg-[#1a1b26]/50 hover:text-white'
                    }`}
                  >
                    <div className={`transition-colors duration-200 ${
                      isActive(item.href) ? 'text-white' : 'text-gray-400'
                    }`}>
                      {item.icon}
                    </div>
                    <span>{item.title}</span>
                  </Link>
                )}
              </div>
            ))}
          </nav>
          
          <div className="p-4 border-t border-gray-800 flex items-center justify-between">
            <Link href="/myProfile"  className='flex items-center text-sm text-gray-400'>
              <FaUserCircle className="w-5 h-5 mr-1 text-[var(--color-light)]" />
              <span>{t('myProfile')}</span>
            </Link>
            <LanguageSwitcher />
          </div>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
