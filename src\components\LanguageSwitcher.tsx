'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { useLocale } from 'next-intl';

interface FlagIcons {
  en: string;
  fr: string;
  ar: string;
}

const flagIcons: FlagIcons = {
  en: '/flags/en.svg',
  fr: '/flags/fr.svg',
  ar: '/flags/ar.svg',
};

interface LanguageSwitcherProps {
  isLoginPage?: boolean;
}

export default function LanguageSwitcher({ isLoginPage }: LanguageSwitcherProps = {}) {
  const locale = useLocale();
  const pathname = usePathname();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'fr', name: 'Français' },
    { code: 'ar', name: 'العربية' },
  ];

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleLanguageChange = (code: string) => {
    const newPathname = pathname.replace(/^\/(en|fr|ar)/, '');
    const href = `/${code}${newPathname}`;
    router.push(href);
    router.refresh();
    setIsOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dropdownRef]);

  const currentLanguage = languages.find((lang) => lang.code === locale)?.name || '';

  return (
    <div className="relative flex items-center h-full">
      <div ref={dropdownRef}>
        <button
          onClick={toggleDropdown}
          className="flex items-center w-28 gap-1 px-3 py-1.5 text-[#94a3b8] bg-[#1e293b] border border-[#1e293b] rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 hover:bg-[#243748]"
        >
          <Image src={flagIcons[locale as keyof FlagIcons]} alt={locale} width={20} height={20} className="rounded-full" />
          {currentLanguage}
        </button>

        {isOpen && (
          <div className={`absolute ${isLoginPage ? 'top-full mt-1' : 'bottom-full mt-1'} right-0 w-24 bg-[#1e293b] rounded-md shadow-lg origin-bottom-right focus:outline-none`} role="menu" aria-orientation="vertical" aria-labelledby="menu-button">
            {languages.map(({ code, name }) => (
              <button
                key={code}
                onClick={() => handleLanguageChange(code)}
                className={`block w-full text-left px-2 py-1 text-sm text-[#94a3b8] hover:bg-gray-100 focus:outline-none focus:bg-gray-100 ${locale === code ? 'bg-gray-100' : ''}`}
                role="menuitem"
              >
                <div className="flex items-center gap-1">
                  <Image src={flagIcons[code as keyof FlagIcons]} alt={code} width={16} height={16} className="rounded-full" />
                  {name}
                </div>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
