"use client";

import { Fa<PERSON>sers, FaMoneyBillWave, FaGamepad, FaUser, FaUserShield, FaUserTie, FaExchangeAlt, FaUserTimes, FaCrown, FaArrowRight, FaArrowLeft } from 'react-icons/fa';
import Link from 'next/link';
import { DashboardStats, Transaction, BannedPlayer, UserSummary } from '@/types/dashboard';
import Layout from '@/components/layout/Layout';
import { useEffect, useState } from 'react';
import { userService } from '@/services/user.service';
import { authUtils } from '@/utils/auth.utils';
import { useTranslations } from 'next-intl';

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const userRole = authUtils.getUserData()?.role;
  const t = useTranslations('Dashboard');

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await userService.getDashboardStats();
        setStats(response.data);
      } catch (error) {
        console.error('Failed to fetch dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
        </div>
      </Layout>
    );
  }

  if (!stats) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-screen">
          <p className="text-red-500">{t('failedToLoad')}</p>
        </div>
      </Layout>
    );
  }

  const renderRoleStats = () => {
    const statsToShow = [];
    const statusStats = [];
    
    if (userRole === 'superowner') {
      statsToShow.push(
        { label: 'owner', value: stats.owner },
        { label: 'superadmin', value: stats.superadmin },
        { label: 'admin', value: stats.admin },
        { label: 'cashier', value: stats.cashier },
        { label: 'player', value: stats.player }
      );
      statusStats.push(
        { label: t('active'), value: stats.active },
        { label: t('banned'), value: stats.banned }
      );
    } else if (userRole === 'owner') {
      statsToShow.push(
        { label: 'superadmin', value: stats.superadmin },
        { label: 'admin', value: stats.admin },
        { label: 'cashier', value: stats.cashier },
        { label: 'player', value: stats.player }
      );
      statusStats.push(
        { label: t('active'), value: stats.active },
        { label: t('banned'), value: stats.banned }
      );
    } else if (userRole === 'superadmin') {
      statsToShow.push(
        { label: 'admin', value: stats.admin },
        { label: 'cashier', value: stats.cashier },
        { label: 'player', value: stats.player }
      );
      statusStats.push(
        { label: t('active'), value: stats.active },
        { label: t('banned'), value: stats.banned }
      );
    }
    else if (userRole === 'admin') {
      statsToShow.push(
        { label: 'cashier', value: stats.cashier },
        { label: 'player', value: stats.player }
      );
      statusStats.push(
        { label: t('active'), value: stats.active },
        { label: t('banned'), value: stats.banned }
      );
    }
    else if (userRole === 'cashier') {
      statsToShow.push(
        { label: t('player'), value: stats.player }
      );
      statusStats.push(
        { label: t('active'), value: stats.active },
        { label: t('banned'), value: stats.banned }
      );
    }

    return (
      <div className="flex gap-8">
        <div className="flex flex-col space-y-2">
          {statsToShow.map((stat, index) => (
            <div key={index} className="text-xs text-gray-600">
            <span
              className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                {
                  owner: "bg-red-500 text-white",
                  superadmin: "bg-blue-500 text-white",
                  admin: "bg-yellow-500 text-black",
                  cashier: "bg-green-500 text-white",
                  player: "bg-gray-500 text-white",
                }[stat.label] || "bg-green-100 text-green-800"
              }`}
            >
              {stat.label}: {stat.value}
            </span>
          </div>
          ))}
        </div>
        <div className="flex flex-col space-y-2">
          {statusStats.map((stat, index) => (
            <div key={index} className="text-xs text-gray-600">
              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                ${stat.label === t('active') ? 'bg-green-100 text-green-800' : 
                  'bg-red-100 text-red-800'}`}>
                {stat.label}: {stat.value}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <Layout>
      <div className="space-y-8">
        <div className="flex flex-col space-y-2">
          <h1 className="text-2xl md:text-3xl font-bold gradient-text">{t('dashboardOverview')}</h1>
          <p className="text-gray-600">{t('welcomeMessage')}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* User Balance Card */}
          <div className="dashboard-card p-6 bg-gray-50 flex flex-col h-full hover-lift">
            <div className="flex flex-col h-full">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">{t('balanceStats')}</p>
                  <p className="text-xl md:text-2xl font-bold text-gray-800">
                    {t('today')}
                  </p>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <FaMoneyBillWave className="w-5 h-5 md:w-6 md:h-6 text-blue-600" />
                </div>
              </div>
              <div className="flex flex-col items-center space-y-2 mt-4">
                <div className="flex gap-6 justify-center w-full">
                  <div className="group relative flex flex-col items-center flex-1">
                    <p className="text-xs text-gray-600">{t('sold')}</p>
                    <p className="text-sm font-bold text-gray-800">{stats.todayTransactions?.sold || '0'}</p>
                    <div className="absolute invisible group-hover:visible bg-gray-700 text-white text-xs rounded py-1 px-2 -bottom-8 left-1/2 transform -translate-x-1/2 whitespace-normal max-w-[200px] break-words">
                      {t('soldTooltip')}
                      <div className="absolute w-2 h-2 bg-gray-700 transform rotate-45 -top-1 left-1/2 -translate-x-1/2"></div>
                    </div>
                  </div>
                  {userRole !== 'owner' && (
                    <div className="group relative flex flex-col items-center flex-1">
                      <p className="text-xs text-gray-600">{t('deposit')}</p>
                      <p className="text-sm font-bold text-gray-800">{stats.todayTransactions?.bought || '0'}</p>
                      <div className="absolute invisible group-hover:visible bg-gray-700 text-white text-xs rounded py-1 px-2 -bottom-8 left-1/2 transform -translate-x-1/2 whitespace-normal max-w-[200px] break-words">
                        {t('depositTooltip')}
                        <div className="absolute w-2 h-2 bg-gray-700 transform rotate-45 -top-1 left-1/2 -translate-x-1/2"></div>
                      </div>
                    </div>
                  )}
                  <div className="group relative flex flex-col items-center flex-1">
                    <p className="text-xs text-gray-600">{t('withdraw')}</p>
                    <p className="text-sm font-bold text-gray-800">{stats.todayTransactions?.took || '0'}</p>
                    <div className="absolute invisible group-hover:visible bg-gray-700 text-white text-xs rounded py-1 px-2 -bottom-8 left-1/2 transform -translate-x-1/2 whitespace-normal max-w-[200px] break-words">
                      {t('withdrawTooltip')}
                      <div className="absolute w-2 h-2 bg-gray-700 transform rotate-45 -top-1 left-1/2 -translate-x-1/2"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="px-6 py-2 border-t border-gray-100 text-right mt-auto">
              <Link 
                href="/transactions/my" 
                className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
              >
                {t('seeTransactions')}
              </Link>
            </div>
          </div>

          {/* Total Games Card */}
          <div className="dashboard-card p-6 bg-gray-50 flex flex-col h-full hover-lift">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">{t('totalGames')}</p>
                <p className="text-xl md:text-2xl font-bold text-gray-800">
                  {stats.totalGames}
                </p>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <FaGamepad className="w-5 h-5 md:w-6 md:h-6 text-purple-600" />
              </div>
            </div>
            {userRole === 'superowner' && (
              <div className="px-6 py-2 border-t border-gray-100 text-right mt-auto">
                <Link 
                  href="/games/list" 
                  className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
                >
                  {t('seeAllGames')}
                </Link>
              </div>
            )}
          </div>

          {/* Total Users Card */}
          <div className="dashboard-card p-6 bg-gray-50 flex flex-col h-full hover-lift">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">{t('totalUsers')}</p>
                <p className="text-xl md:text-2xl font-bold text-gray-800">
                  {stats.totalUsers}
                </p>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <FaUsers className="w-5 h-5 md:w-6 md:h-6 text-green-600" />
              </div>
            </div>
            <div className="flex flex-col items-center space-y-2 mt-4">
              {renderRoleStats()}
            </div>
            <div className="px-6 py-2 border-t border-gray-100 text-right mt-auto">
              <Link 
                href="/users/search" 
                className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
              >
                {t('seeAllUsers')}
              </Link>
            </div>
          </div>
        </div>

      {/* Transactions and Banned Players Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Last Transactions Card */}
        <div className="dashboard-card bg-gray-50 flex flex-col h-full">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg md:text-xl font-semibold text-gray-800">{t('lastTransactions')}</h2>
          </div>
          <div className="px-6 py-4 space-y-1 flex-grow">
            {stats.lastTransactions.length > 0 ? (
              stats.lastTransactions.slice(0, 5).map((transaction: Transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-1 hover:bg-gray-100 rounded-lg transition-all duration-200">
                  <div className="flex items-center space-x-4">
                    <div className={`p-2 rounded-full ${
                      transaction.type === 'add' ? 'bg-red-100' : 'bg-green-100'
                    }`}>
                      <FaExchangeAlt className={`w-4 h-4 transform rotate-180 ${
                        transaction.type === 'add' ? 'text-red-600' : 'text-green-600'
                      }`} />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-800">
                        {transaction.type === 'add' ? t('added') : t('deducted')} {transaction.amount} {transaction.toUser.currency}
                      </p>
                      <p className="text-xs text-gray-500">
                        {transaction.fromUser.username} 
                        {transaction.type === 'add' ? (
                          <FaArrowRight className="inline mx-1 w-3 h-3" />
                        ) : (
                          <FaArrowLeft className="inline mx-1 w-3 h-3" />
                        )}
                        {transaction.toUser.username}
                      </p>
                      <div className="text-xs text-gray-400 mt-1">
                        {new Date(transaction.createdAt).toLocaleString()}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-4 text-center text-gray-500 flex-grow">
                {t('noTransactionRecords')}
              </div>
            )}
          </div>
          <div className="px-6 py-4 border-t border-gray-100 text-right">
            <Link 
              href="/transactions/my" 
              className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
            >
              {t('seeTransactions')}
            </Link>
          </div>
        </div>

        {/* Last Banned Players Card */}
        <div className="dashboard-card bg-gray-50 flex flex-col h-full">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg md:text-xl font-semibold text-gray-800">{t('lastBannedPlayers')}</h2>
          </div>
          <div className="px-6 py-4 space-y-1 flex-grow">
            {stats.lastBannedPlayers.length > 0 ? (
              stats.lastBannedPlayers.slice(0, 5).map((user: BannedPlayer) => (
                <div key={user.id} className="flex items-center justify-between p-1 hover:bg-gray-100 rounded-lg transition-all duration-200">
                  <div className="flex items-center space-x-4">
                    <div className="bg-red-100 p-2 rounded-full">
                      <FaUserTimes className="w-4 h-4 text-red-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-800">{user.username}</p>
                      <p className="text-xs text-gray-500">{t('banned')}</p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-4 text-center text-gray-500 flex-grow">
                {t('noUsersBanned')}
              </div>
            )}
          </div>
          <div className="px-6 py-4 border-t border-gray-100 text-right">
            <Link
              href="/users/search" 
              className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
            >
              {t('seeAllUsers')}
            </Link>
          </div>
        </div>
      </div>

      {/* Top Users Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Top Superadmins Card */}
        {userRole === 'superowner' && (
          <div className="dashboard-card p-6 bg-gray-50 flex flex-col h-full">
            <div className="flex items-center space-x-2 mb-6 pb-4 border-b border-gray-200">
              <FaCrown className="w-5 h-5 text-red-500" />
              <h2 className="text-lg md:text-xl font-semibold text-red-800">{t('topOwners')}</h2>
            </div>
            <div className="px-6 py-4 space-y-1 flex-grow">
              {stats.topOwners.length > 0 ? (
                stats.topOwners.slice(0, 5).map((user: UserSummary) => (
                <div key={user.id} className="flex items-center justify-between p-2 hover:bg-gray-200 rounded-lg transition-all duration-200">
                  <div className="flex items-center space-x-4">
                    <div className="bg-red-200 p-2 rounded-full">
                      <FaUserShield className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-gray-900">{user.username}</p>
                      <p className="text-xs text-gray-600">{t('balance')}: <span className="font-bold">{user.balance} TND</span></p>
                    </div>
                  </div>
                </div>
              ))
              ) : (
                <div className="p-4 text-center text-gray-500 flex-grow">
                  {t('noOwnerRecords')}
                </div>
              )}
            </div>
            <div className="px-6 py-2 border-t border-gray-100 text-right">
              <Link 
                href="/users/search" 
                className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
              >
                {t('seeAllUsers')}
              </Link>
            </div>
          </div>
        )}
        
        {/* Top Cashiers Card */}
        {(userRole === 'superowner' || userRole === 'owner') && (
          <div className="dashboard-card p-6 bg-gray-50 flex flex-col h-full">
            <div className="flex items-center space-x-2 mb-6 pb-4 border-b border-gray-200">
              <FaCrown className="w-5 h-5 text-blue-500" />
              <h2 className="text-lg md:text-xl font-semibold text-blue-800">{t('topSuperadmins')}</h2>
            </div>
            <div className="px-6 py-4 space-y-1 flex-grow">
              {stats.topSuperadmins.length > 0 ? (
                stats.topSuperadmins.slice(0, 5).map((user: UserSummary) => (
                <div key={user.id} className="flex items-center justify-between p-2 hover:bg-gray-200 rounded-lg transition-all duration-200">
                  <div className="flex items-center space-x-4">
                    <div className="bg-blue-200 p-2 rounded-full">
                      <FaUserTie className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-gray-900">{user.username}</p>
                      <p className="text-xs text-gray-600">{t('balance')}: <span className="font-bold">{user.balance} TND</span></p>
                    </div>
                  </div>
                </div>
              ))
              ) : (
                <div className="p-4 text-center text-gray-500 flex-grow">
                  {t('noSuperadminRecords')}
                </div>
              )}
            </div>
            <div className="px-6 py-2 border-t border-gray-100 text-right">
              <Link 
                href="/users/search" 
                className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
              >
                {t('seeAllUsers')}
              </Link>
            </div>
          </div>
        )}

        {/* Top Players Card */}
        {(userRole === 'superowner' || userRole === 'owner' || userRole === 'superadmin') && (
          <div className="dashboard-card p-6 bg-gray-50 flex flex-col h-full">
            <div className="flex items-center space-x-2 mb-6 pb-4 border-b border-gray-200">
              <FaCrown className="w-5 h-5 text-yellow-500" />
              <h2 className="text-lg md:text-xl font-semibold text-yellow-800">{t('topAdmins')}</h2>
            </div>
            <div className="px-6 py-4 space-y-1 flex-grow">
              {stats.topAdmins.length > 0 ? (
                stats.topAdmins.slice(0, 5).map((user: UserSummary) => (
                <div key={user.id} className="flex items-center justify-between p-2 hover:bg-gray-200 rounded-lg transition-all duration-200">
                  <div className="flex items-center space-x-4">
                    <div className="bg-yellow-200 p-2 rounded-full">
                      <FaUser className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-gray-900">{user.username}</p>
                      <p className="text-xs text-gray-600">{t('balance')}: <span className="font-bold">{user.balance} TND</span></p>
                    </div>
                  </div>
                </div>
              ))
              ) : (
                <div className="p-4 text-center text-gray-500 flex-grow">
                  {t('noAdminRecords')}
                </div>
              )}
            </div>
            <div className="px-6 py-2 border-t border-gray-100 text-right">
              <Link 
                href="/users/search" 
                className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
              >
                {t('seeAllUsers')}
              </Link>
            </div>
          </div>
        )}
         {(userRole === 'superowner' || userRole === 'owner' || userRole === 'superadmin' || userRole === 'admin') && (
          <div className="dashboard-card p-6 bg-gray-50 flex flex-col h-full">
            <div className="flex items-center space-x-2 mb-6 pb-4 border-b border-gray-200">
              <FaCrown className="w-5 h-5 text-green-500" />
              <h2 className="text-lg md:text-xl font-semibold text-green-800">{t('topCashiers')}</h2>
            </div>
            <div className="px-6 py-4 space-y-1 flex-grow">
              {stats.topCashiers.length > 0 ? (
                stats.topCashiers.slice(0, 5).map((user: UserSummary) => (
                <div key={user.id} className="flex items-center justify-between p-2 hover:bg-gray-200 rounded-lg transition-all duration-200">
                  <div className="flex items-center space-x-4">
                    <div className="bg-green-200 p-2 rounded-full">
                      <FaUser className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-gray-900">{user.username}</p>
                      <p className="text-xs text-gray-600">{t('balance')}: <span className="font-bold">{user.balance} TND</span></p>
                    </div>
                  </div>
                </div>
              ))
              ) : (
                <div className="p-4 text-center text-gray-500 flex-grow">
                  {t('noCashierRecords')}
                </div>
              )}
            </div>
            <div className="px-6 py-2 border-t border-gray-100 text-right">
              <Link 
                href="/users/search" 
                className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
              >
                {t('seeAllUsers')}
              </Link>
            </div>
          </div>
        )}
        {(userRole === 'superowner' || userRole === 'owner' || userRole === 'superadmin' || userRole === 'admin' || userRole === 'cashier') && (
          <div className="dashboard-card p-6 bg-gray-50 flex flex-col h-full">
            <div className="flex items-center space-x-2 mb-6 pb-4 border-b border-gray-200">
              <FaCrown className="w-5 h-5 text-gray-500" />
              <h2 className="text-lg md:text-xl font-semibold text-gray-800">{t('topPlayers')}</h2>
            </div>
            <div className="px-6 py-4 space-y-1 flex-grow">
              {stats.topPlayers.length > 0 ? (
                stats.topPlayers.slice(0, 5).map((user: UserSummary) => (
                <div key={user.id} className="flex items-center justify-between p-2 hover:bg-gray-200 rounded-lg transition-all duration-200">
                  <div className="flex items-center space-x-4">
                    <div className="bg-gray-200 p-2 rounded-full">
                      <FaUser className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-gray-900">{user.username}</p>
                      <p className="text-xs text-gray-600">{t('balance')}: <span className="font-bold">{user.balance} TND</span></p>
                    </div>
                  </div>
                </div>
              ))
              ) : (
                <div className="p-4 text-center text-gray-500 flex-grow">
                  {t('noPlayerRecords')}
                </div>
              )}
            </div>
            <div className="px-6 py-2 border-t border-gray-100 text-right">
              <Link
                href="/users/search" 
                className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
              >
                {t('seeAllUsers')}
              </Link>
            </div>
          </div>
        )}
      </div>
          
        </div>
    </Layout>
  );
}
