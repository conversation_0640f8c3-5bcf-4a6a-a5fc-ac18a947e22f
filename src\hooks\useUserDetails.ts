import { useState, useEffect } from 'react';
import axios from 'axios';
import { authService } from '@/services/auth.service';
import { authUtils } from '@/utils/auth.utils';
import { User } from '@/services/user.service';

export const useUserDetails = () => {
  const [userDetails, setUserDetails] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchUserDetails = async () => {
    try {
      const response = await authService.getUserDetails();
      if (response.status === 'success') {
        setUserDetails(response.data);
        authUtils.updateUserData(response.data);
      }
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : 'Failed to fetch user details';
      setError(new Error(message));
      console.error(message, error);
      
      if (axios.isAxiosError(error) && 
          (error.response?.status === 401 || 
           (error.response?.data as { message?: string })?.message?.includes('Token has been invalidated'))) {
        authUtils.clearAuthData();
        window.location.href = '/';
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // First try to get from local storage
    const cachedUser = authUtils.getUserData();
    if (cachedUser) {
      setUserDetails(cachedUser);
    }
    
    // Then fetch fresh data
    fetchUserDetails();
  }, []);

  return {
    userDetails,
    loading,
    error,
    refresh: fetchUserDetails
  };
};
