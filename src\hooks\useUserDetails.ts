import { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { authService } from '@/services/auth.service';
import { authUtils } from '@/utils/auth.utils';
import { User } from '@/services/user.service';

// Global state to prevent multiple simultaneous calls
let globalUserDetails: User | null = null;
let globalLoading = false;
let globalError: Error | null = null;
let lastFetchTime = 0;
const BALANCE_REFRESH_INTERVAL = 60 * 1000; // 1 minute for balance refresh
const USER_DETAILS_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache for other user details
const activeListeners = new Set<() => void>();
let balanceRefreshTimer: NodeJS.Timeout | null = null;

// Global fetch function to prevent multiple simultaneous calls
const globalFetchUserDetails = async (forceRefresh: boolean = false): Promise<void> => {
  if (globalLoading) {
    return; // Already fetching, don't make another call
  }

  const now = Date.now();
  const cachedUser = authUtils.getUserData();

  // If we have recent cached data and not forcing refresh, use it
  if (!forceRefresh && cachedUser && globalUserDetails && (now - lastFetchTime) < USER_DETAILS_CACHE_DURATION) {
    return;
  }

  globalLoading = true;
  globalError = null;

  // Notify all listeners that loading started
  activeListeners.forEach(listener => listener());

  try {
    const response = await authService.getUserDetails();
    if (response.status === 'success') {
      globalUserDetails = response.data;
      authUtils.updateUserData(response.data);
      lastFetchTime = now;
    }
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'Failed to fetch user details';
    globalError = new Error(message);
    console.error(message, error);

    if (axios.isAxiosError(error) &&
        (error.response?.status === 401 ||
         (error.response?.data as { message?: string })?.message?.includes('Token has been invalidated'))) {
      authUtils.clearAuthData();
      window.location.href = '/';
    }
  } finally {
    globalLoading = false;
    // Notify all listeners that loading finished
    activeListeners.forEach(listener => listener());
  }
};

// Balance refresh function that runs every minute
const startBalanceRefreshTimer = () => {
  if (balanceRefreshTimer) {
    clearInterval(balanceRefreshTimer);
  }

  balanceRefreshTimer = setInterval(async () => {
    // Only refresh if we have user data and someone is listening
    if (globalUserDetails && activeListeners.size > 0) {
      const now = Date.now();
      // Only refresh if it's been more than 1 minute since last fetch
      if ((now - lastFetchTime) >= BALANCE_REFRESH_INTERVAL) {
        console.log('Auto-refreshing balance...');
        await globalFetchUserDetails(true);
      }
    }
  }, BALANCE_REFRESH_INTERVAL);
};

// Stop balance refresh timer
const stopBalanceRefreshTimer = () => {
  if (balanceRefreshTimer) {
    clearInterval(balanceRefreshTimer);
    balanceRefreshTimer = null;
  }
};

export const useUserDetails = () => {
  const [userDetails, setUserDetails] = useState<User | null>(globalUserDetails);
  const [loading, setLoading] = useState(globalLoading);
  const [error, setError] = useState<Error | null>(globalError);
  const listenerRef = useRef<() => void>();

  const updateState = () => {
    setUserDetails(globalUserDetails);
    setLoading(globalLoading);
    setError(globalError);
  };

  useEffect(() => {
    // Create listener function
    listenerRef.current = updateState;
    activeListeners.add(listenerRef.current);

    // Start balance refresh timer if this is the first listener
    if (activeListeners.size === 1) {
      startBalanceRefreshTimer();
    }

    // Initialize with cached data if available
    const cachedUser = authUtils.getUserData();
    if (cachedUser && !globalUserDetails) {
      globalUserDetails = cachedUser;
      setUserDetails(cachedUser);
    }

    // Check if we need to fetch fresh data
    const now = Date.now();
    const shouldFetch = !globalUserDetails ||
                       (now - lastFetchTime) > USER_DETAILS_CACHE_DURATION ||
                       (!cachedUser && !globalLoading);

    if (shouldFetch) {
      globalFetchUserDetails();
    } else {
      // Use existing data
      setUserDetails(globalUserDetails);
      setLoading(false);
    }

    // Cleanup listener on unmount
    return () => {
      if (listenerRef.current) {
        activeListeners.delete(listenerRef.current);
      }

      // Stop balance refresh timer if no more listeners
      if (activeListeners.size === 0) {
        stopBalanceRefreshTimer();
      }
    };
  }, []);

  const refresh = async () => {
    await globalFetchUserDetails(true); // Force refresh
  };

  return {
    userDetails,
    loading,
    error,
    refresh
  };
};

// Export function for manual balance refresh (can be used by other components)
export const refreshUserBalance = async (): Promise<void> => {
  await globalFetchUserDetails(true);
};
