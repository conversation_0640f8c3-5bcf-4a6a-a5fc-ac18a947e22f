"use client";

import { Fragment, useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { useForm } from 'react-hook-form';
import { FaUser, FaMoneyBill, FaTimes } from 'react-icons/fa';
import { toast } from 'sonner';
import { cashflowService } from '@/services/cashflow.service';
import { useUserDetails } from '@/hooks/useUserDetails';
import { useTranslations } from 'next-intl';

interface User {
  id: number;
  username: string;
  balance: string;
  currency: string;
  role: string;
}

interface CashManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User;
  mode: 'add' | 'deduct';
  onBalanceUpdate: () => Promise<void>;
}

interface FormData {
  amount: string;
}

export default function CashManagementModal({ 
  isOpen, 
  onClose, 
  user, 
  mode,
  onBalanceUpdate 
}: CashManagementModalProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [previewBalance, setPreviewBalance] = useState(parseFloat(user.balance));
  const [loggedUserBalance, setLoggedUserBalance] = useState(0);
  const [previewLoggedUserBalance, setPreviewLoggedUserBalance] = useState(0);
  const t = useTranslations('manageCashModal');
  
  const { 
    register, 
    handleSubmit, 
    watch,
    formState: { errors },
    reset,
  } = useForm<FormData>({
    defaultValues: {
      amount: ''
    }
  });

  const amount = watch('amount');

  // Fetch logged user's balance using authService.getUserDetails
  const { userDetails } = useUserDetails();
  useEffect(() => {
    if (userDetails) {
      const balance = parseFloat(userDetails.balance);
      setLoggedUserBalance(balance);
      setPreviewLoggedUserBalance(balance);
    }
  }, [userDetails]);

  useEffect(() => {
    if (amount) {
      const numAmount = parseFloat(amount);
      if (!isNaN(numAmount)) {
        setPreviewBalance(mode === 'add' 
          ? parseFloat(user.balance) + numAmount
          : parseFloat(user.balance) - numAmount
        );
        
        // Update preview of logged user's balance
        setPreviewLoggedUserBalance(mode === 'add' 
          ? loggedUserBalance - numAmount
          : loggedUserBalance + numAmount
        );
      }
    } else {
      setPreviewBalance(parseFloat(user.balance));
      setPreviewLoggedUserBalance(loggedUserBalance);
    }
  }, [amount, mode, user.balance, loggedUserBalance]);

  const validateAmount = (value: string) => {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) {
      return t('validNumber');
    }
    if (numValue <= 0.99) {
      return t('amountGreaterThan');
    }
    if (mode === 'deduct' && numValue > parseFloat(user.balance)) {
      return t('insufficientBalance');
    }
    return true;
  };

  const onSubmit = async (data: FormData) => {
    setIsProcessing(true);
    try {
      const amount = parseFloat(data.amount);
  
      if (mode === 'add') {
        await cashflowService.addCash({ 
          toUserId: user.id, 
          amount 
        });
      } else {
        await cashflowService.deductCash({ 
          fromUserId: user.id, 
          amount 
        });
      }
  
      toast.success(t(mode === 'add' ? 'addSuccess' : 'deductSuccess'));
      await onBalanceUpdate();
      onClose();
      reset();
    } catch (error: unknown) {
      let errorMessage = 'transactionFailed'; // Default translation key for a generic failure message
      
      if (error instanceof Error) {
        // Check if the error message matches the expected response format and translate it
        if (error.message.includes('Insufficient balance')) {
          errorMessage = 'insufficientBalance'; // Use the translation key for insufficient balance error
        } else {
          errorMessage = 'transactionFailed'; // Fallback to the error message itself
        }
      }
  
      // Translate the error message using `t`
      const translatedMessage = t(errorMessage);
      toast.error(translatedMessage);
    } finally {
      setIsProcessing(false);
    }
  };
  
  

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/25 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-2xl transition-all">
                <div className="bg-gray-900 text-white p-6 rounded-t-2xl">
                  <div className="flex justify-between items-center">
                    <h3 className="text-xl font-bold">
                      {mode === 'add' ? t('addFunds') : t('deductFunds')}
                    </h3>
                    <button
                      onClick={onClose}
                      className="text-white/80 hover:text-white transition-colors"
                    >
                      <FaTimes className="w-6 h-6" />
                    </button>
                  </div>
                  <p className="text-sm mt-1 opacity-90">
                    {mode === 'add' 
                      ? t('transferFunds')
                      : t('deductFundsFromUser')}
                  </p>
                </div>

                <div className="p-6">
                  {/* User Info Section */}
                  <div className="bg-gray-50 rounded-xl p-4 mb-6 border border-gray-100">
                    <div className="flex items-center space-x-3 mb-2">
                      <FaUser className="text-gray-500" />
                      <span className="font-medium text-gray-700">@{user.username}</span>
                    </div>
                    <div className="text-2xl font-bold text-gray-900">
                      {parseFloat(user.balance).toFixed(2)} {user.currency}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">{t('currentBalance')}</div>
                  </div>

                  <form onSubmit={handleSubmit(onSubmit)}>
                    {/* Amount Input */}
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('amountTo')} {mode === 'add' ? t('add') : t('deduct')} ({user.currency})
                      </label>
                      <div className="relative">
                        <FaMoneyBill className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="number"
                          step="0.01"
                          min="0.99"
                          {...register('amount', {
                            required: t('amountRequired'),
                            validate: validateAmount
                          })}
                          className={`block w-full pl-10 pr-3 py-2.5 rounded-lg border ${
                            errors.amount ? 'border-red-300' : 'border-gray-200'
                          } focus:outline-none focus:ring-2 ${
                            mode === 'add' ? 'focus:ring-blue-500' : 'focus:ring-purple-500'
                          } focus:border-transparent transition-all`}
                          placeholder="0.00"
                        />
                      </div>
                      {errors.amount && (
                        <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
                      )}
                    </div>

                    {/* Balance Preview Section */}
                    <div className="space-y-4 mb-6">
                      {/* Target User Balance */}
                      <div className="bg-gray-50 rounded-xl p-4 border border-gray-100">
                        <div className="text-sm text-gray-500 mb-1">{t('newUserBalance')}</div>
                        <div className={`text-2xl font-bold ${
                          previewBalance >= parseFloat(user.balance) 
                            ? 'text-green-600' 
                            : 'text-red-600'
                        }`}>
                          {previewBalance.toFixed(2)} {user.currency}
                        </div>
                      </div>

                      {/* Your Balance */}
                      <div className="bg-gray-50 rounded-xl p-4 border border-gray-100">
                        <div className="flex items-center space-x-2 mb-1">
                          <FaUser className="text-gray-400" />
                          <div className="text-sm text-gray-500">{t('yourNewBalance')}</div>
                        </div>
                        <div className="flex items-baseline space-x-2">
                          <div className={`text-2xl font-bold ${
                            previewLoggedUserBalance >= loggedUserBalance
                              ? 'text-green-600'
                              : 'text-red-600'
                          }`}>
                            {previewLoggedUserBalance.toFixed(2)} {user.currency}
                          </div>
                          {previewLoggedUserBalance !== loggedUserBalance && (
                            <div className={`text-sm ${
                              previewLoggedUserBalance > loggedUserBalance
                                ? 'text-green-600'
                                : 'text-red-600'
                            }`}>
                              ({previewLoggedUserBalance > loggedUserBalance ? '+' : '-'}
                              {Math.abs(previewLoggedUserBalance - loggedUserBalance).toFixed(2)})
                            </div>
                          )}
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                          {mode === 'add' 
                            ? t('deductedFromYourBalance')
                            : t('addedToYourBalance')}
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-3">
                      <button
                        type="button"
                        onClick={onClose}
                        className="px-5 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all"
                      >
                        {t('cancel')}
                      </button>
                      <button
                        type="submit"
                        disabled={isProcessing}
                        className={`px-5 py-2.5 text-sm font-medium text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all ${
                          'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                        } ${isProcessing ? 'opacity-75 cursor-not-allowed' : ''}`}
                      >
                        {isProcessing ? t('processing') : t('confirm')}
                      </button>
                    </div>
                  </form>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
