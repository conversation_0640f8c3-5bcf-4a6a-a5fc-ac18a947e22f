import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import createMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';

// This array contains paths that don't require authentication
const publicPaths = ['/', '/api/users/refresh-token'];

const i18nMiddleware = createMiddleware(routing);

export function middleware(request: NextRequest) {
  // First apply i18n middleware
  const i18nResponse = i18nMiddleware(request);

  // Bypass middleware for static files
  if (/\.[^/]+$/.test(request.nextUrl.pathname)) {
    return NextResponse.next();
  }



  if (i18nResponse) return i18nResponse;
   const accessToken = request.cookies.get('accessToken');
   const refreshToken = request.cookies.get('refreshToken');
   const currentPath = request.nextUrl.pathname;
   const domain = request.nextUrl.hostname;

   // Allow access to public paths without authentication
   if (publicPaths.includes(currentPath)) {
     return NextResponse.next();
   }

   // If user has tokens and tries to access login page, redirect to dashboard
   if ((accessToken || refreshToken) && currentPath === '/') {
     const dashboardUrl = new URL('/dashboard', request.url);
     dashboardUrl.hostname = domain;
     return NextResponse.redirect(dashboardUrl);
   }

   // Check if user is authenticated
   // With HTTP-only cookies, we can only check for presence of cookies, not validate them
   const hasAccessToken = !!accessToken;
   const hasRefreshToken = !!refreshToken;

   // If no access token but has refresh token, let client handle refresh
   if (!hasAccessToken && hasRefreshToken && !currentPath.startsWith('/api/')) {
     const response = NextResponse.next();
     response.headers.set('x-needs-token-refresh', 'true');
     return response;
   }

   // If no tokens at all and trying to access protected route, redirect to login
   if (!hasAccessToken && !hasRefreshToken && !publicPaths.includes(currentPath)) {
     const loginUrl = new URL('/', request.url);
     loginUrl.hostname = domain;
     loginUrl.searchParams.set('from', currentPath);
     return NextResponse.redirect(loginUrl);
   }

   // If has tokens and trying to access login page, redirect to dashboard
   if ((hasAccessToken || hasRefreshToken) && currentPath === '/') {
     const dashboardUrl = new URL('/dashboard', request.url);
     dashboardUrl.hostname = domain;
     return NextResponse.redirect(dashboardUrl);
   }

   // Allow access to all other routes - the backend will validate the tokens
   return NextResponse.next();
 }

 // Configure which paths should be protected
 export const config = {
   matcher: [
     /*
      * Match all paths except:
      * 1. /_next (Next.js internals)
      * 2. /_static (inside /public)
      * 3. /_vercel (Vercel internals)
      * 4. /favicon.ico, /sitemap.xml (static files)
      */
     '/((?!_next|_static|_vercel|favicon.ico|sitemap.xml).*)',
   ],
 };
