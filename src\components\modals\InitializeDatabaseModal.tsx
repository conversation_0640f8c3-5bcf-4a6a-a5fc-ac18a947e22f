"use client";

import { Fragment, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { motion } from 'framer-motion';
import { FaDatabase, FaTimes } from 'react-icons/fa';
import { toast } from 'sonner';
import { GameService } from '@/services/game.service';
import { useTranslations } from 'next-intl';

interface InitializeDatabaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInitialize: () => Promise<void>;
}

export default function InitializeDatabaseModal({ 
  isOpen, 
  onClose,
  onInitialize
}: InitializeDatabaseModalProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const t = useTranslations('initializeDatabaseModal');

  const handleInitialize = async () => {
    setIsProcessing(true);
    try {
      await GameService.initGames();
      toast.success(t('databaseInitializedSuccessfully'));
      await onInitialize();
      onClose();
    } catch (error: unknown) {
      console.error('Database initialization error:', error);
      const message = error instanceof Error ? error.message : t('failedToInitializeDatabase');
      toast.error(message);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-xl transition-all">
                <div className="bg-gray-900 text-white p-6 rounded-t-2xl">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <FaDatabase className="text-blue-500" />
                      {t('initializeDatabase')}
                    </h3>
                    <button
                      onClick={onClose}
                      className="text-gray-300 hover:text-white transition-colors"
                    >
                      <FaTimes className="w-5 h-5" />
                    </button>
                  </div>
                </div>

                <div className="p-6">
                  <div className="space-y-4">
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <p className="text-sm text-yellow-800">
                        {t('warningMessage')}
                      </p>
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                      onClick={onClose}
                    >
                      {t('cancel')}
                    </button>
                    <motion.button
                      whileTap={{ scale: 0.95 }}
                      type="button"
                      className="inline-flex justify-center items-center gap-2 rounded-md border border-transparent px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                      onClick={handleInitialize}
                      disabled={isProcessing}
                    >
                      <FaDatabase />
                      {isProcessing 
                        ? t('processing')
                        : t('initialize')
                      }
                    </motion.button>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
