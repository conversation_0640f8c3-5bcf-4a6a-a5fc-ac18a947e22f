import axios from 'axios';
import { authUtils } from '@/utils/auth.utils';
import { API_BASE_URL } from '@/config';
import { authService } from '@/services/auth.service';

export interface Game {
  id: number;
  name: string;
  title: string;
  img: string;
  device: string;
  categories: string[];
}

export interface PaginationInfo {
  total: number;
  page: number;
  totalPages: number;
  limit: number;
}

export interface GameSearchFilters {
  name?: string;
  title?: string;
  categories?: string;
  device?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'title';
  sortOrder?: 'ASC' | 'DESC';
}

export interface PaginatedResponse {
  games: Game[];
  pagination: PaginationInfo;
}

export interface GameHistory {
  id: number;
  gameId: string;
  name: string;
  bet: number;
  win: number;
  action: string;
  date: Date;
  balance_before: number;
  balance_after: number;
  user: {
    id: number;
    username: string;
  };
}

// Create axios instance with interceptors
const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/api/v1/games`,
  withCredentials: true, // Important for cookies
});

// Add request interceptor to include CSRF token
apiClient.interceptors.request.use(
  async config => {
    // Skip for GET requests
    if (config.method?.toLowerCase() === 'get') {
      return config;
    }

    // For non-GET requests, ensure we have a valid token
    await authUtils.ensureValidToken();

    // Add CSRF token to headers for non-GET requests
    const csrfToken = authUtils.getCsrfToken();
    if (csrfToken) {
      config.headers['X-CSRF-Token'] = csrfToken;
    }

    return config;
  },
  error => Promise.reject(error)
);

// Add response interceptor for 401 errors
apiClient.interceptors.response.use(
  response => response,
  async error => {
    if (error.response?.status === 401) {
      try {
        // Try to refresh the token
        await authService.refreshToken();

        // Retry the original request
        const newConfig = { ...error.config };

        // Update CSRF token for the retry
        if (newConfig.method?.toLowerCase() !== 'get') {
          const csrfToken = authUtils.getCsrfToken();
          if (csrfToken) {
            newConfig.headers['X-CSRF-Token'] = csrfToken;
          }
        }

        return axios(newConfig);
      } catch (refreshError) {
        // If refresh fails, clear auth and redirect
        console.log(refreshError)
        authUtils.clearAuthData();
        window.location.href = '/';
      }
    }
    return Promise.reject(error);
  }
);

export class GameService {

  static async getProvidersAndCategories(type: 'categories' | 'titles'): Promise<string[]> {
    try {
      const response = await apiClient.get('/prov-cate', {
        params: { type }
      });
      return response.data;
    } catch (error: unknown) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw new Error('Authentication failed. Please log in again.');
        }
      }
      throw new Error(error instanceof Error ? error.message : 'An unknown error occurred');
    }
  }

  static async initGames() {
    try {
      const response = await apiClient.post('/init');
      return response.data;
    } catch (error: unknown) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          if ((error.response.data as { message?: string })?.message === 'Insufficient permissions') {
            throw new Error('You do not have permission to initialize the database. Only owners can perform this action.');
          }
          throw new Error('Authentication failed. Please log in again.');
        }
      }
      throw new Error(error instanceof Error ? error.message : 'An unknown error occurred');
    }
  }

  static async searchGames(filters: GameSearchFilters): Promise<PaginatedResponse> {
    try {
      const response = await apiClient.get('/search', {
        params: {
          ...filters,
          categories: filters.categories
        }
      });
      return response.data;
    } catch (error: unknown) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw new Error('Authentication failed. Please log in again.');
        }
      }
      throw new Error(error instanceof Error ? error.message : 'An unknown error occurred');
    }
  }

  static async getGameHistoryByUserId(
    _viewerId: number, // Prefix with underscore to indicate it's not used
    targetUserId: number,
    params: {
      page?: number;
      limit?: number;
      startDate?: string;
      endDate?: string;
    }
  ): Promise<{ history: GameHistory[]; total: number }> {
    try {
      const response = await apiClient.get(`/history/user/${targetUserId}`, {
        params: {
          page: params.page,
          limit: params.limit,
          startDate: params.startDate,
          endDate: params.endDate
        }
      });

      return {
        history: response.data.data,
        total: response.data.pagination.total
      };
    } catch (error: unknown) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw new Error('Authentication failed. Please log in again.');
        }
        if (error.response?.status === 403) {
          throw new Error('You do not have permission to view this user\'s game history.');
        }
        if (error.response?.status === 404) {
          throw new Error('User not found.');
        }
      }
      throw new Error(error instanceof Error ? error.message : 'An unknown error occurred');
    }
  }
}
