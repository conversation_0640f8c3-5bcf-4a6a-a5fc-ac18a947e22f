"use client";

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { FaHistory, FaSearch } from 'react-icons/fa';
import { format } from 'date-fns';
import { toast } from 'sonner';
import GameHistoryModal from '@/components/modals/GameHistoryModal';
import { userService, type User, type PaginationInfo } from '@/services/user.service';
import Layout from '@/components/layout/Layout';
import { useTranslations } from 'next-intl';

export default function BettingHistoryPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [, setIsLoading] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    totalPages: 1,
    limit: 10
  });

  const t = useTranslations('Betting-history');

  const fetchUsers = useCallback(async (username?: string, page: number = 1) => {
    setIsLoading(true);
    try {
      const response = await userService.searchUsers({
        username,
        page,
        limit: pagination.limit,
        roles: 'player'
      });

      setUsers(response.data.users);
      setPagination(response.data.pagination);
    } catch (error: unknown) {
      console.error('Error fetching users:', error);
      if (error instanceof Error && 'response' in error) {
        const axiosError = error as { response: { data: { message: string } } };
        toast.error(axiosError.response.data.message);
      } else {
        toast.error('Failed to fetch users');
      }
    } finally {
      setIsLoading(false);
    }
  }, [pagination.limit]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers, pagination.limit]);

  useEffect(() => {
    if (searchTerm.length >= 4) {
      const debounce = setTimeout(() => {
        fetchUsers(searchTerm);
      }, 300);
      return () => clearTimeout(debounce);
    } else if (searchTerm.length === 0) {
      fetchUsers();
    }
  }, [searchTerm, fetchUsers]);

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd/MM/yyyy HH:mm');
  };

  const handleHistoryClick = (user: User) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
  };

  return (
    <Layout>
      <div className="space-y-8">
        <div className="flex flex-col space-y-2">
          <h1 className="text-2xl md:text-3xl font-bold gradient-text">{t('title')}</h1>
          <p className="text-gray-600">{t('description')}</p>
        </div>

        <div className="w-full max-w-[95%] bg-white rounded-lg shadow-lg hover:shadow-2xl transition-shadow duration-300 p-6">
          <div className="mb-6">
            <div className="relative">
              <input
                type="text"
                placeholder={t('searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 pl-10 pr-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <FaSearch className="absolute left-3 top-3 text-gray-400" />
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('username')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('role')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('balance')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('status')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('createdAt')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.map((user, index) => (
                  <motion.tr
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{user.username}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        ${ 
                          user.role === 'player' ? 'bg-gray-500 text-white' : 
                          'bg-gray-500 text-white'}`}>
                        {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.balance} {user.currency}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        user.is_banned
                          ? 'bg-red-100 text-red-800'
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {user.is_banned ? t('banned') : t('active')}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(user.created_at || new Date().toISOString())}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <motion.button
                        whileTap={{ scale: 0.95 }}
                        onClick={() => handleHistoryClick(user)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 focus:outline-none focus:ring-2 focus:ring-offset-2 w-24"
                      >
                        <FaHistory className="mr-1" />
                        {t('history')}
                      </motion.button>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="mt-4 flex items-center justify-center">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => {
                    const newPage = pagination.page - 1;
                    if (newPage >= 1) {
                      fetchUsers(searchTerm, newPage);
                    }
                  }}
                  disabled={pagination.page <= 1}
                  className={`flex items-center px-3 py-1 rounded-md text-sm font-medium ${
                    pagination.page <= 1
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                  }`}
                >
                  {t('previous')}
                </button>
                <span className="text-sm text-gray-700">
                  {t('showing')} {((pagination.page - 1) * pagination.limit) + 1} {t('to')}{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} {t('of')} {pagination.total} {t('results')}
                </span>
                <button
                  onClick={() => {
                    const newPage = pagination.page + 1;
                    if (newPage <= pagination.totalPages) {
                      fetchUsers(searchTerm, newPage);
                    }
                  }}
                  disabled={pagination.page >= pagination.totalPages}
                  className={`flex items-center px-3 py-1 rounded-md text-sm font-medium ${
                    pagination.page >= pagination.totalPages
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                  }`}
                >
                  {t('next')}
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Game History Modal */}
        {selectedUser && (
          <GameHistoryModal
            isOpen={isModalOpen}
            onClose={closeModal}
            userId={selectedUser.id}
          />
        )}
      </div>
    </Layout>
  );
}
