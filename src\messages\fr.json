{"Login": {"title": "Admin Casino", "welcome": "BIENVENUE!!", "subtitle": "Développez votre entreprise et vos revenus de manière simple, efficace et rentable", "signIn": "Connectez-vous à votre compte", "username": "Nom d'utilisateur", "password": "Mot de passe", "rememberMe": "Se souvenir de moi", "signInButton": "Se connecter", "signingIn": "Connexion en cours...", "noAccount": "Pas de compte?", "contactAdmin": "Contacter l'administrateur", "enterUsername": "Entrez votre nom d'utilisateur", "enterPassword": "Entrez votre mot de passe", "invalidUsernameOrPassword": "Nom d'utilisateur ou mot de passe invalide", "playersNotAllowed": "Les joueurs ne sont pas autorisés à accéder au panneau d'administration.", "invalidTokenFormat": "Format de jeton invalide reçu du serveur", "failedToStoreAuthData": "Échec du stockage des données d'authentification. Veuillez réessayer."}, "Layout": {"balance": "Solde", "profile": "Profil", "logout": "Déconnexion", "waitBeforeRefresh": "Veuillez patienter moins de 60 secondes avant d'actualiser à nouveau le solde", "failedToUpdateBalance": "Échec de la mise à jour du solde"}, "Dashboard": {"dashboardOverview": "Aperçu du tableau de bord", "welcomeMessage": "Bienvenue sur votre tableau de bord de gestion de casino", "balanceStats": "Statistiques de solde", "today": "Au<PERSON><PERSON>'hui :", "sold": "Vendu", "soldTooltip": "Total des fonds transférés à vos sous-comptes directs aujourd'hui", "deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "depositTooltip": "Fonds reçus de votre compte parent aujourd'hui", "withdraw": "Retrait", "withdrawTooltip": "Fonds retirés de vos sous-comptes directs aujourd'hui", "seeTransactions": "Voir les transactions →", "totalGames": "Nombre total de jeux", "seeAllGames": "Voir tous les jeux →", "totalUsers": "Nombre total d'utilisateurs", "seeAllUsers": "Voir tous les utilisateurs →", "active": "Actif", "banned": "<PERSON><PERSON>", "lastTransactions": "Dernières transactions", "added": "<PERSON><PERSON><PERSON>", "deducted": "Déduit", "noTransactionRecords": "Aucun enregistrement de transaction", "lastBannedPlayers": "Derniers joueurs bannis", "noUsersBanned": "Aucun utilisateur n'est actuellement banni", "topOwners": "Meilleurs Propriétaires", "topSuperadmins": "Meilleurs Superadmins", "topAdmins": "Meilleurs Admins", "topCashiers": "Meilleurs Caissiers", "topPlayers": "Meilleurs Joueurs", "balance": "Solde", "noSuperownerRecords": "Aucun enregistrement de superowner ", "noOwnerRecords": "Aucun enregistrement de owner", "noSuperadminRecords": "Aucun enregistrement de superadmin", "noAdminRecords": "Aucun enregistrement de admin", "noCashierRecords": "Aucun enregistrement de caissier", "noPlayerRecords": "Aucun enregistrement de joueur", "failedToLoad": "Échec du chargement des données du tableau de bord", "superadmin": "Superadmin", "cashier": "Cais<PERSON>r", "player": "<PERSON><PERSON><PERSON>"}, "Betting-history": {"title": "Historique des paris", "description": "Afficher l'historique des paris de l'utilisateur", "searchPlaceholder": "Rechercher par nom d'utilisateur (min. 4 caractères)", "username": "Nom d'utilisateur", "role": "R<PERSON><PERSON>", "balance": "Solde", "status": "Statut", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "Actions", "history": "Historique", "banned": "<PERSON><PERSON>", "active": "Actif", "previous": "< Précédent", "next": "Suivant >", "showing": "Affichage", "to": "à", "of": "de", "results": "résultats"}, "Game-list": {"title": "Liste des jeux", "gamesManagement": "Gestion des jeux", "manageAndView": "<PERSON><PERSON>rer et afficher les listes de jeux", "gamesDatabase": "Base de données des jeux", "initializeGamesDatabase": "Initialiser la base de données des jeux", "initializeDatabase": "Initialiser la base de données", "gamesList": "Liste des jeux", "viewAndManage": "Aff<PERSON><PERSON> et gérer les listes de jeux", "searchGames": "Rechercher des jeux par nom (min. 3 caractères)", "allProviders": "Tous les fournisseurs", "allCategories": "Toutes les catégories", "id": "ID", "name": "Nom", "provider": "Fournisseur", "image": "Image", "categories": "Catégories", "loadingGames": "Chargement des jeux...", "noGamesAvailable": "Aucun jeu disponible", "previous": "< Précédent", "next": "Suivant >", "showing": "Affichage", "to": "à", "of": "de", "results": "résultats", "fullSizeGame": "<PERSON>u en taille réelle", "close": "X", "failedToFetchGames": "Échec de la récupération des jeux", "databaseInitializedSuccessfully": "Base de données initialisée avec succès", "failedToInitializeDatabase": "Échec de l'initialisation de la base de données"}, "myProfile": {"myProfile": "Mon Profil", "profileInformation": "<PERSON><PERSON><PERSON><PERSON> et gérer les informations de votre profil", "username": "Nom d'utilisateur", "role": "R<PERSON><PERSON>", "balance": "Solde", "accountStatus": "Statut du compte", "accountIsActive": "Le compte est actif"}, "password-form": {"changePassword": "Changer le mot de passe", "currentPassword": "Mot de passe actuel", "newPassword": "Nouveau mot de passe", "confirmNewPassword": "Confirmer le nouveau mot de passe", "changePasswordButton": "Changer le mot de passe", "changingPassword": "Changement de mot de passe...", "passwordRequirements": "Exigences du mot de passe :", "atLeast8Characters": "Au moins 8 caractères", "uppercaseLowercaseLetters": "Lettres majuscules et minuscules", "atLeastOneNumber": "Au moins un chiffre", "currentPasswordRequired": "Le mot de passe actuel est requis", "newPasswordRequired": "Le nouveau mot de passe est requis", "passwordTooShort": "Le mot de passe doit contenir au moins 8 caractères", "passwordComplexity": "Le mot de passe doit contenir au moins un minuscules et au moins un chiffre", "confirmPasswordRequired": "Veuillez confirmer votre mot de passe", "passwordsDoNotMatch": "Les mots de passe ne correspondent pas", "passwordChangedSuccessfully": "Mot de passe modifié avec succès", "pleaseLoginAgain": "Veuillez vous reconnecter."}, "myTransactions": {"myTransactions": "Mes Transactions", "viewAndManage": "Afficher et gérer votre historique de transactions", "refreshTransactions": "Actualiser les transactions", "toggleFilters": "Basculer les filtres", "type": "Type", "all": "Tous", "add": "Ajouter", "deduct": "D<PERSON><PERSON><PERSON>", "startDate": "Date de début", "endDate": "Date de fin", "clear": "<PERSON><PERSON><PERSON><PERSON>", "id": "ID", "amount": "<PERSON><PERSON>", "from": "De", "to": "À", "date": "Date", "noTransactionsFound": "Aucune transaction trouvée", "previous": "< Précédent", "next": "Suivant >", "showing": "Affichage", "toText": "à", "of": "de", "results": "résultats"}, "transactionHistoryModal": {"title": "Historique des transactions", "amount": "<PERSON><PERSON>", "from": "De", "type": "Type", "to": "À", "date": "Date", "noTransactionsFound": "Aucune transaction trouvée", "previous": "< Précédent", "next": "Suivant >", "showing": "Affichage", "of": "de", "results": "résultats", "clear": "<PERSON><PERSON><PERSON><PERSON>", "transactionHistory": "Historique des transactions - {username}"}, "userTransactions": {"title": "Transactions des utilisateurs", "description": "Afficher et gérer l'historique des transactions des utilisateurs", "searchPlaceholder": "Rechercher des utilisateurs (min. 4 caractères)", "hideFilters": "Masquer les filtres", "showFilters": "Afficher les filtres", "roleFilter": "Filtrer par rôle", "statusFilter": "Filtrer par statut", "allUsers": "Tous les utilisateurs", "activeUsers": "Utilisateurs actifs", "bannedUsers": "Utilisateurs bannis", "dateRange": "Période", "startDate": "Date de début", "endDate": "Date de fin", "clear": "<PERSON><PERSON><PERSON><PERSON>", "username": "Nom d'utilisateur", "role": "R<PERSON><PERSON>", "balance": "Solde", "status": "Statut", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "Actions", "viewHistory": "Afficher l'historique", "noUsersFound": "Aucun utilisateur trouvé", "previous": "< Précédent", "next": "Suivant >", "showing": "Affichage", "toText": "à", "of": "de", "results": "résultats", "active": "Actif", "banned": "<PERSON><PERSON>"}, "userBanManagement": {"banManagement": "Gestion des bannissements", "manageUserAccess": "<PERSON><PERSON><PERSON> l'accès et les restrictions des utilisateurs", "searchUsers": "Rechercher des utilisateurs (min. 4 caractères)", "username": "Nom d'utilisateur", "role": "R<PERSON><PERSON>", "balance": "Solde", "status": "Statut", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "Actions", "unban": "Débannir", "ban": "Bannir", "previous": "< Précédent", "next": "Suivant >", "showing": "Affichage", "to": "à", "of": "de", "results": "résultats", "banned": "<PERSON><PERSON>", "active": "Actif"}, "userBanManagementModal": {"unbanUser": "Débannir l'utilisateur", "banUser": "Bannir l'utilisateur", "userInformation": "Informations sur l'utilisateur", "thisActionWillUnban": "Cette action débannira cet utilisateur et tous ses descendants.", "thisActionWillBan": "Cette action bannira cet utilisateur et tous ses descendants.", "cancel": "Annuler", "processing": "Traitement en cours...", "userUnbanned": "{username} et ses descendants ont été débannis", "userBanned": "{username} et ses descendants ont été bannis", "banStatusUpdateFailed": "Échec de la mise à jour du statut du bannissement"}, "manageCashPage": {"cashManagement": "Gestion de la trésorerie", "efficientlyManage": "<PERSON><PERSON><PERSON> efficacement les soldes et les transactions des utilisateurs", "searchByUsername": "Rechercher par nom d'utilisateur (min. 4 caractères)", "username": "Nom d'utilisateur", "role": "R<PERSON><PERSON>", "balance": "Solde", "status": "Statut", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "Actions", "add": "Ajouter", "deduct": "D<PERSON><PERSON><PERSON>", "previous": "< Précédent", "next": "Suivant >", "showing": "Affichage", "to": "à", "of": "de", "results": "résultats", "banned": "<PERSON><PERSON>", "active": "Actif"}, "manageCashModal": {"addFunds": "Ajouter des fonds", "deductFunds": "Déduire des fonds", "transferFunds": "Transférer des fonds de votre compte à l'utilisateur", "deductFundsFromUser": "Déduire des fonds de l'utilisateur vers votre compte", "currentBalance": "Solde actuel", "amountTo": "<PERSON><PERSON>", "add": "Ajouter", "deduct": "D<PERSON><PERSON><PERSON>", "newUserBalance": "Nouveau solde de l'utilisateur", "yourNewBalance": "Votre nouveau solde", "deductedFromYourBalance": "Ce montant sera déduit de votre solde", "addedToYourBalance": "Ce montant sera ajouté à votre solde", "cancel": "Annuler", "confirm": "Confirmer", "processing": "Traitement en cours...", "amountRequired": "Le montant est requis", "validNumber": "Veuillez entrer un nombre valide", "amountGreaterThan": "Le montant doit être supérieur à 0.99", "insufficientBalance": "Solde insuffisant", "addSuccess": "Argent a<PERSON>té avec succès", "deductSuccess": "Argent déduit avec succès", "transactionFailed": "Échec du traitement de la transaction"}, "createUserPage": {"title": "Créer un nouvel utilisateur", "description": "Ajoutez de nouveaux utilisateurs et attribuez-leur des rôles dans le système", "usernameLabel": "Nom d'utilisateur", "usernamePlaceholder": "Entrez un nom d'utilisateur (5 à 20 caractères)", "usernameRequirements": "Se<PERSON> les lettres, les chiffres et les underscores sont autorisés", "roleLabel": "R<PERSON><PERSON>", "passwordLabel": "Mot de passe", "passwordRequirements": "Minimum requis :", "createUserButton": "C<PERSON>er un utilisateur", "creating": "Création en cours...", "userCreatedSuccessfully": "Utilisateur c<PERSON>é avec succès", "newAccountCreated": "Un nouveau compte {role} a <PERSON>té créé", "usernameLengthError": "Le nom d'utilisateur doit comporter entre 5 et 20 caractères", "usernameFormatError": "Le nom d'utilisateur ne peut contenir que des lettres, des chiffres et des underscores", "passwordLengthError": "Le mot de passe doit comporter au moins 8 caractères", "passwordComplexityError": "Le mot de passe doit contenir au moins une lettre et un chiffre", "roleRequiredError": "Le rôle est obligatoire", "currencyLengthError": "La devise doit comporter 3 caractères", "atLeast8Characters": "Au moins 8 caractères", "uppercaseLowercaseLetters": "Lettres majuscules et minuscules", "atLeastOneNumber": "Au moins un chiffre"}, "userSearchPage": {"usersManagement": "Gestion des utilisateurs", "searchAndManage": "Re<PERSON>cher et gérer les utilisateurs", "searchUsersPlaceholder": "Rechercher des utilisateurs (min. 4 caractères)", "hideFilters": "Masquer les filtres", "showFilters": "Afficher les filtres", "roleFilter": "Filtrer par rôle", "statusFilter": "Filtrer par statut", "allUsers": "Tous les utilisateurs", "activeUsers": "Utilisateurs actifs", "bannedUsers": "Utilisateurs bannis", "dateRange": "Période", "startDate": "Date de début", "endDate": "Date de fin", "clear": "<PERSON><PERSON><PERSON><PERSON>", "username": "Nom d'utilisateur", "role": "R<PERSON><PERSON>", "balance": "Solde", "status": "Statut", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "Actions", "manage": "<PERSON><PERSON><PERSON>", "noUsersFound": "Aucun utilisateur trouvé", "previous": "< Précédent", "next": "Suivant >", "showing": "Affichage", "to": "à", "of": "de", "results": "résultats", "banned": "<PERSON><PERSON>", "active": "Actif", "all": "Tous"}, "footerComponent": {"copyRight": ".", "allRightsReserved": "©"}, "sidebarMenu": {"dashboard": "Tableau de bord", "users": "Utilisateurs", "createUser": "C<PERSON>er un utilisateur", "manageUserCash": "<PERSON><PERSON><PERSON> l'argent des utilisateurs", "banUnbanUsers": "Bannir/Débannir des utilisateurs", "allUsers": "Tous les utilisateurs", "transactions": "Transactions", "myTransactions": "Mes transactions", "userTransactions": "Transactions des utilisateurs", "games": "<PERSON><PERSON>", "gameList": "Liste de jeux", "bettingHistory": "Historique des paris", "myProfile": "Mon profil", "online": "En ligne"}, "gameHistoryModal": {"title": "Historique des jeux", "from": "De", "to": "À", "clear": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON><PERSON>", "bet": "<PERSON><PERSON>", "win": "<PERSON><PERSON>", "action": "Action", "date": "Date", "balanceBefore": "Solde avant", "balanceAfter": "Solde après", "previous": "Précédent", "page": "Page", "of": "de", "next": "Suivant", "close": "<PERSON><PERSON><PERSON>", "userNotAuthenticated": "Utilisateur non authentifié", "failedToFetchGameHistory": "Échec de la récupération de l'historique des jeux"}, "initializeDatabaseModal": {"initializeDatabase": "Initialiser la base de données", "warningMessage": "Cette action initialisera la base de données des jeux. Cette action est irréversible.", "cancel": "Annuler", "initialize": "Initialiser", "processing": "Traitement en cours...", "databaseInitializedSuccessfully": "Base de données initialisée avec succès", "failedToInitializeDatabase": "Échec de l'initialisation de la base de données"}, "refreshButtonUi": {"balanceUpdated": "Solde mis à jour avec succès"}, "BanManagementTab": {"thisActionWillUnbanDescendants": "Cette action débloquera cet utilisateur et tous ses descendants.", "thisActionWillUnban": "Cette action débloquera cet utilisateur.", "thisActionWillBanDescendants": "Cette action bloquera cet utilisateur et tous ses descendants.", "thisActionWillBan": "Cette action bloquera cet utilisateur.", "unbanUserDescendants": "Débloquer l'utilisateur et ses descendants", "unbanUser": "Débloquer l'utilisateur", "banUserDescendants": "Bloquer l'utilisateur et ses descendants", "banUser": "Bloquer l'utilisateur", "userUnbanned": "{username} et ses descendants ont été débloqués", "userBanned": "{username} et ses descendants ont été bloqués", "banStatusUpdateFailed": "Échec de la mise à jour du statut de blocage", "currentlyBanned": "<PERSON><PERSON><PERSON> blo<PERSON>", "currentlyActive": "Actuellement actif", "processing": "Traitement en cours..."}, "BettingHistoryTab": {"from": "De", "to": "À", "clear": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON><PERSON>", "bet": "<PERSON><PERSON>", "win": "<PERSON><PERSON>", "action": "Action", "date": "Date", "balanceBefore": "Solde avant", "balanceAfter": "Solde après", "noGameHistoryFound": "Aucun historique de jeu trouvé", "showingPage": "Affichage de la page", "ofText": "de", "previous": "Précédent", "next": "Suivant", "failedToFetchGameHistory": "Échec de la récupération de l'historique des jeux", "userNotAuthenticated": "Utilisateur non authentifié"}, "CashManagementTab": {"validNumber": "Veuillez entrer un nombre valide", "amountGreaterThan": "Le montant doit être supérieur à 0.99", "insufficientBalance": "Solde insuffisant", "addSuccess": "Argent a<PERSON>té avec succès", "addFailed": "Échec de l'ajout d'argent", "deductSuccess": "Argent déduit avec succès", "deductFailed": "Échec de la déduction d'argent", "unavailableTitle": "Gestion de trésorerie non disponible", "unavailableDescription": "Vous ne pouvez pas gérer le solde de cet utilisateur lorsqu'il est banni. Débannissez d'abord l'utilisateur pour effectuer des opérations de trésorerie.", "addCashTitle": "A<PERSON>ter de l'argent", "amountToAddLabel": "Montant à ajouter", "enterAmountPlaceholder": "<PERSON><PERSON><PERSON> le montant", "previewBalanceLabel": "Aperçu du solde", "processing": "Traitement en cours...", "addCashButton": "A<PERSON>ter de l'argent", "deductCashTitle": "Dé<PERSON><PERSON> de l'argent", "amountToDeductLabel": "Montant à déduire", "deductCashButton": "Dé<PERSON><PERSON> de l'argent"}, "TransactionHistoryTab": {"from": "De", "to": "À", "clear": "<PERSON><PERSON><PERSON><PERSON>", "type": "Type", "amount": "<PERSON><PERSON>", "date": "Date", "noTransactionsFound": "Aucune transaction trouvée", "showingPage": "Affichage de la page", "ofText": "de", "previous": "Précédent", "next": "Suivant", "failedToFetchTransactions": "Échec de la récupération des transactions"}, "UserManageModal": {"manageUser": "<PERSON><PERSON><PERSON> l'utilisateur", "basicInformation": "Informations de base", "username": "Nom d'utilisateur", "role": "R<PERSON><PERSON>", "status": "Statut", "banned": "<PERSON><PERSON>", "active": "Actif", "inactive": "Inactif", "accountDetails": "<PERSON>é<PERSON> du compte", "balance": "Solde", "lastLogin": "Dernière connexion", "createdAt": "<PERSON><PERSON><PERSON>", "childrens": "<PERSON><PERSON><PERSON>", "hisResponsible": "Nom du gestionnaire", "userInfo": "Informations sur l'utilisateur", "manageCash": "<PERSON><PERSON><PERSON> l'argent", "banManagement": "Gestion des bannissements", "transactions": "Transactions", "bettingHistory": "Historique des paris", "tree": "Arbre", "failedToFetchProfile": "Échec de la récupération du profil utilisateur", "info": "Informations", "cash": "<PERSON><PERSON><PERSON> l'argent", "ban": "Gestion des bannissements", "betting": "Historique des paris"}, "UserTreeTab": {"failedToFetchTreeData": "Échec de la récupération des données de l'arbre", "banned": "<PERSON><PERSON>", "balance": "Solde", "manageUser": "<PERSON><PERSON><PERSON> l'utilisateur", "userTree": "Arbre de l'utilisateur", "searchUsersPlaceholder": "Rechercher des utilisateurs...", "minBalance": "Solde minimum", "allStatuses": "Tous les statuts", "bannedOnly": "Bannis seulement", "activeOnly": "Actifs seulement", "noMatchingUsers": "Aucun utilisateur correspondant trouvé"}}