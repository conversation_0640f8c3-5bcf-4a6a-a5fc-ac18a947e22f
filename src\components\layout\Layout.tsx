"use client";

import { useState, useEffect, useRef, createContext, useCallback } from 'react';
import { useUserDetails, refreshUserBalance } from '@/hooks/useUserDetails';

import { useTranslations } from 'next-intl';
import { FaBars, FaSync, FaUserCircle, FaMoneyBillWave, FaChevronDown, FaUser, FaSignOutAlt } from 'react-icons/fa';
import { Menu } from '@headlessui/react';
import Sidebar from './Sidebar';
import Footer from './Footer';
import { authUtils } from '@/utils/auth.utils';
import { authService } from '@/services/auth.service';
import { toast } from 'sonner';
import Link from 'next/link';

interface LayoutContextType {
  handleBalanceRefresh: () => Promise<void>;
}

export const LayoutContext = createContext<LayoutContextType>({
  handleBalanceRefresh: async () => {}
});

export default function Layout({ children }: { children: React.ReactNode }) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [mounted, setMounted] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [balance, setBalance] = useState<string>('0.00');
  const [currency, setCurrency] = useState<string>('');
  const [userData, setUserData] = useState<{
    username?: string;
    role?: string;
    balance?: string;
    currency?: string;
  } | null>(null);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const t = useTranslations('Layout');

  const { userDetails } = useUserDetails();

  useEffect(() => {
    setMounted(true);
    if (userDetails) {
      setUserData(userDetails);
      setBalance(userDetails.balance || '0.00');
      setCurrency(userDetails.currency || 'TND');
    }
  }, [userDetails]);

  const handleResize = () => {
    if (window.innerWidth >= 1024) {
      setIsSidebarOpen(true);
    } else {
      setIsSidebarOpen(false);
    }
  };

  const handleScroll = () => {
    const offset = window.scrollY;
    setScrolled(offset > 0);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
      // Menu handles its own state
    }
  };



  const handleTokenRefresh = useCallback(async () => {
    try {
      await authService.refreshToken();
      // Update user data from localStorage
      setUserData(authUtils.getUserData());
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Token refresh failed:', errorMessage);
      handleLogout();
    }
  }, []);

  useEffect(() => {
    const checkTokenRefresh = () => {
      const needsRefresh = document.cookie.includes('x-needs-token-refresh=true');
      if (needsRefresh) {
        handleTokenRefresh();
      }
    };

    const init = () => {
      handleResize();
      checkTokenRefresh();
    };

    init();
    window.addEventListener('resize', handleResize);
    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('scroll', handleScroll); // Add scroll event listener
  }, [handleTokenRefresh]);

  useEffect(() => {
    // Remove the automatic balance refresh interval to prevent unnecessary API calls
    // The balance will be updated through the useUserDetails hook and manual refresh
    return () => {
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const handleLogout = async () => {
    try {
      await authService.logout();
    } catch (error) {
      console.error('Logout failed:', error);
      authUtils.clearAuthData();
      window.location.href = '/';
    }
  };

  const refreshCooldown = 10000; // Reduced to 10 seconds since we have global management

  const handleBalanceRefresh = useCallback(async () => {
    const now = Date.now();
    const lastRefresh = sessionStorage.getItem('lastBalanceRefresh');

    if (lastRefresh && (now - Number(lastRefresh)) < refreshCooldown) {
      toast.warning(t('waitBeforeRefresh'));
      return;
    }

    sessionStorage.setItem('lastBalanceRefresh', now.toString());
    setIsRefreshing(true);

    try {
      // Use the global refresh function for manual refresh
      await refreshUserBalance();
      //toast.success('Balance updated successfully');
    } catch {
      toast.error(t('failedToUpdateBalance'));
    } finally {
      setIsRefreshing(false);
    }
  }, [t]);

  // Removed automatic balance refresh on pathname change to prevent multiple API calls
  // The balance will be updated through the useUserDetails hook and manual refresh

  if (!mounted) return null;

  return (
    <LayoutContext.Provider value={{ handleBalanceRefresh }}>
      <div className={`min-h-screen bg-[var(--bg-primary)] ${scrolled ? 'scrolled-class' : ''}`}>
        {/* Overlay for small screens */}
        {isSidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setIsSidebarOpen(false)}
          />
        )}
        <Sidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} />

        {/* Header */}
        <header
          className={`fixed top-0 lg:pl-64 left-0 right-0 z-40 transition-all duration-300 ease-in-out
           `}
        >
          <div className="flex items-center justify-between h-14 px-4">
            <div className="flex items-center gap-4">
              <button
                onClick={() => setIsSidebarOpen(!isSidebarOpen)}
                className="lg:hidden p-2 rounded-md hover:bg-[#1a1b26]/50 transition-colors duration-200"
              >
                <FaBars className="h-6 w-6 text-[var(--color-light)] hover:text-white" />
              </button>

            </div>

            <div className="flex items-center">
              {/* Balance Section */}
              <div className="hidden sm:flex items-center gap-2 bg-[var(--color-mid)] rounded-lg px-3 py-1.5 shadow-sm mr-4 hover:bg-[var(--color-mid)]/80 transition-all duration-300 hover:shadow-md hover:-translate-y-0.5 border border-[var(--color-mid)]">
                <FaMoneyBillWave className="w-5 h-5 text-[var(--color-light)]" />
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-[#a1a1aa]">{t('balance')}</span>
                  <span className="text-sm font-medium text-white">
                    {balance} {currency}
                  </span>
                  <button
                    onClick={handleBalanceRefresh}
                    disabled={isRefreshing}
                    className="p-1 rounded-full hover:bg-gray-200 transition-colors"
                  >
                    <div className="relative">
                      <FaSync
                        className={`w-4 h-4 text-[var(--color-light)] transition-transform ${isRefreshing ? 'animate-spin' : ''}`}
                      />
                    </div>
                  </button>
                </div>
              </div>

              {/* User Dropdown */}
              <Menu as="div" className="relative">
                <Menu.Button className="flex items-center gap-2 rounded-lg p-2 transition-colors">
                  <div className="flex items-center gap-2 bg-[var(--color-mid)] rounded-lg px-3 py-1.5 shadow-sm transition-all duration-300 hover:shadow-md hover:-translate-y-0.5 border border-[var(--color-mid)]">
                    <FaUserCircle className="w-5 h-5 text-[var(--color-light)]" />
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-white">{userData?.username}</span>
                      <span className="text-xs font-medium px-2 py-1 rounded-full bg-[var(--color-mid)] text-[var(--color-accent)] border border-[var(--color-mid)] shadow-sm">
                        {userData?.role}
                      </span>
                    </div>
                    <FaChevronDown className="w-4 h-4 text-[var(--color-light)]" />
                  </div>
                </Menu.Button>

                <Menu.Items className="absolute right-0 mt-2 w-64 origin-top-right bg-[#1a1b26] rounded-lg shadow-xl ring-1 ring-[#2a2b3a] focus:outline-none border border-[#2a2b3a]">
                  <div className="py-1">
                    {/* Mobile Only: Show balance and role */}
                    <div className="sm:hidden px-4 py-2">
                      <div
                        className="flex items-center gap-2 text-sm text-[#a1a1aa] cursor-pointer"
                        onClick={handleBalanceRefresh}
                      >
                        <span className="whitespace-nowrap">{t('balance')}: {balance} {currency}</span>
                        <div className="p-1 hover:bg-gray-100 rounded-full">
                          <FaSync className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                        </div>
                      </div>
                    </div>
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          href="/myProfile"
                          className={`${
                            active ? 'bg-[#2a2b3a]' : ''
                          } block w-full text-left px-4 py-2 text-sm text-[#a1a1aa] hover:text-white flex items-center gap-2`}
                        >
                          <FaUser className="w-4 h-4 mr-2" />
                          {t('profile')}
                        </Link>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <button
                          onClick={handleLogout}
                          className={`${
                            active ? 'bg-[#2a2b3a]' : ''
                          } block w-full text-left px-4 py-2 text-sm text-[#a1a1aa] hover:text-white flex items-center gap-2`}
                        >
                          <FaSignOutAlt className="w-4 h-4 mr-2" />
                          {t('logout')}
                        </button>
                      )}
                    </Menu.Item>

                  </div>
                </Menu.Items>
              </Menu>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="pt-20 lg:pl-64 min-h-screen flex flex-col">
          <div className="px-4 py-6 flex-grow">
            {children}
          </div>
          <Footer />
        </main>
      </div>
    </LayoutContext.Provider>
  );
}
