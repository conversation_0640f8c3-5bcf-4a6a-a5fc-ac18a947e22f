'use client';

import { useState, useContext, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { FaPlus, FaMinus, FaMoneyBill, FaBan } from 'react-icons/fa';
import { toast } from 'sonner';
import { cashflowService } from '@/services/cashflow.service';
import { LayoutContext } from '@/components/layout/Layout';
import { UserProfile } from '@/services/user.service';
import { useTranslations } from 'next-intl';

interface CashManagementTabProps {
  userProfile: UserProfile;
  onBalanceUpdate: () => Promise<void>;
}

interface FormData {
  amount: string;
}

export default function CashManagementTab({ userProfile, onBalanceUpdate }: CashManagementTabProps) {
  const [isProcessingAdd, setIsProcessingAdd] = useState(false);
  const [isProcessingDeduct, setIsProcessingDeduct] = useState(false);
  const [previewAddBalance, setPreviewAddBalance] = useState(parseFloat(userProfile.balance));
  const [previewDeductBalance, setPreviewDeductBalance] = useState(parseFloat(userProfile.balance));
  const { handleBalanceRefresh } = useContext(LayoutContext);
  const t = useTranslations('CashManagementTab');

  const addForm = useForm<FormData>({
    defaultValues: { amount: '' }
  });

  const deductForm = useForm<FormData>({
    defaultValues: { amount: '' }
  });

  // Watch amount changes for previews
  const addAmount = addForm.watch('amount');
  const deductAmount = deductForm.watch('amount');

  // Update balance previews
  useEffect(() => {
    const numAddAmount = parseFloat(addAmount || '0');
    setPreviewAddBalance(!isNaN(numAddAmount) 
      ? parseFloat(userProfile.balance) + numAddAmount
      : parseFloat(userProfile.balance)
    );
  }, [addAmount, userProfile.balance]);

  useEffect(() => {
    const numDeductAmount = parseFloat(deductAmount || '0');
    setPreviewDeductBalance(!isNaN(numDeductAmount)
      ? parseFloat(userProfile.balance) - numDeductAmount
      : parseFloat(userProfile.balance)
    );
  }, [deductAmount, userProfile.balance]);

  const validateAmount = (value: string) => {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) {
      return t('validNumber');
    }
    if (numValue <= 0.99) {
      return t('amountGreaterThan');
    }
    return true;
  };

  const validateDeductAmount = (value: string) => {
    const baseValidation = validateAmount(value);
    if (baseValidation !== true) return baseValidation;
    
    const numValue = parseFloat(value);
    if (numValue > parseFloat(userProfile.balance)) {
      return t('insufficientBalance');
    }
    return true;
  };

  const handleAddCash = async (data: FormData) => {
    setIsProcessingAdd(true);
    try {
      await cashflowService.addCash({
        toUserId: userProfile.id,
        amount: parseFloat(data.amount)
      });

      toast.success(t('addSuccess'));
      addForm.reset();
      await Promise.all([
        onBalanceUpdate(),
        handleBalanceRefresh()
      ]);
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : t('addFailed');
      toast.error(message);
    } finally {
      setIsProcessingAdd(false);
    }
  };

  const handleDeductCash = async (data: FormData) => {
    setIsProcessingDeduct(true);
    try {
      await cashflowService.deductCash({
        fromUserId: userProfile.id,
        amount: parseFloat(data.amount)
      });

      toast.success(t('deductSuccess'));
      deductForm.reset();
      await Promise.all([
        onBalanceUpdate(),
        handleBalanceRefresh()
      ]);
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : t('deductFailed');
      toast.error(message);
    } finally {
      setIsProcessingDeduct(false);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-4">
      {userProfile.is_banned ? (
        <div className="col-span-2 bg-red-50 rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-center text-red-800">
            <div className="text-center">
              <FaBan className="w-12 h-12 mx-auto mb-4 text-red-500" />
              <h3 className="text-lg font-semibold mb-2">{t('unavailableTitle')}</h3>
              <p className="text-red-600">
                {t('unavailableDescription')}
              </p>
            </div>
          </div>
        </div>
      ) : (
        <>
          {/* Add Cash Section */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6 shadow-sm relative"
          >
            <div className="flex items-center space-x-2 text-green-700 mb-4">
              <FaPlus className="w-4 h-4" />
              <h3 className="text-lg font-semibold">{t('addCashTitle')}</h3>
            </div>

            <form onSubmit={addForm.handleSubmit(handleAddCash)} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('amountToAddLabel')}
                </label>
                <div className="relative">
                  <input
                    type="number"
                    step="0.01"
                    {...addForm.register('amount', { validate: validateAmount })}
                    className={`block w-full px-4 py-2 text-sm border rounded-md focus:ring-green-500 focus:border-green-500 ${
                      addForm.formState.errors.amount ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder={t('enterAmountPlaceholder')}
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <FaMoneyBill className="text-gray-400" />
                  </div>
                </div>
                {addForm.formState.errors.amount && (
                  <p className="mt-1 text-sm text-red-600">{addForm.formState.errors.amount.message}</p>
                )}
              </div>

              <div className="text-sm text-gray-600">
                {t('previewBalanceLabel')}: <span className="font-medium text-green-600">{previewAddBalance.toFixed(2)} {userProfile.currency}</span>
              </div>

              <motion.button
                whileTap={{ scale: 0.95 }}
                type="submit"
                disabled={isProcessingAdd}
                className={`w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 ${
                  isProcessingAdd ? 'opacity-75 cursor-not-allowed' : ''
                }`}
              >
                {isProcessingAdd ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>{t('processing')}</span>
                  </div>
                ) : (
                  t('addCashButton')
                )}
              </motion.button>
            </form>
          </motion.div>

          {/* Vertical Divider */}
          <div className="hidden md:block absolute left-1/2 top-4 bottom-4 w-px bg-gray-200"></div>

          {/* Deduct Cash Section */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-6 shadow-sm"
          >
            <div className="flex items-center space-x-2 text-red-700 mb-4">
              <FaMinus className="w-4 h-4" />
              <h3 className="text-lg font-semibold">{t('deductCashTitle')}</h3>
            </div>

            <form onSubmit={deductForm.handleSubmit(handleDeductCash)} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('amountToDeductLabel')}
                </label>
                <div className="relative">
                  <input
                    type="number"
                    step="0.01"
                    {...deductForm.register('amount', { validate: validateDeductAmount })}
                    className={`block w-full px-4 py-2 text-sm border rounded-md focus:ring-red-500 focus:border-red-500 ${
                      deductForm.formState.errors.amount ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder={t('enterAmountPlaceholder')}
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <FaMoneyBill className="text-gray-400" />
                  </div>
                </div>
                {deductForm.formState.errors.amount && (
                  <p className="mt-1 text-sm text-red-600">{deductForm.formState.errors.amount.message}</p>
                )}
              </div>

              <div className="text-sm text-gray-600">
                {t('previewBalanceLabel')}: <span className="font-medium text-red-600">{previewDeductBalance.toFixed(2)} {userProfile.currency}</span>
              </div>

              <motion.button
                whileTap={{ scale: 0.95 }}
                type="submit"
                disabled={isProcessingDeduct}
                className={`w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 ${
                  isProcessingDeduct ? 'opacity-75 cursor-not-allowed' : ''
                }`}
              >
                {isProcessingDeduct ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>{t('processing')}</span>
                  </div>
                ) : (
                  t('deductCashButton')
                )}
              </motion.button>
            </form>
          </motion.div>
        </>
      )}
    </div>
  );
}
