export interface Transaction {
  id: number;
  amount: number;
  type: 'add' | 'deduct';
  createdAt: Date;
  updatedAt: Date;
  fromUser: {
    id: number;
    username: string;
  };
  toUser: {
    id: number;
    username: string;
    currency: string;
  };
}

export interface BannedPlayer {
  id: number;
  username: string;
  isBanned: boolean;
  updatedAt: Date;
}

export interface UserSummary {
  id: number;
  username: string;
  balance: number;
}

export interface DashboardStats {
  totalUsers: number;
  owner: number;
  superadmin: number;
  admin: number;
  cashier: number;
  player: number;
  banned: number;
  active: number;
  userBalance: string;
  totalGames: number;
  lastTransactions: Transaction[];
  lastBannedPlayers: BannedPlayer[];
  topOwners: UserSummary[];
  topSuperadmins: UserSummary[];
  topAdmins: UserSummary[];
  topCashiers: UserSummary[];
  topPlayers: UserSummary[];
  todayTransactions?: {
    sold: number;
    bought: number;
    took: number;
  };
  currency?: string;
}
