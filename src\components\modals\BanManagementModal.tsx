"use client";

import { Fragment, useState } from 'react';
import axios from 'axios';
import { Dialog, Transition } from '@headlessui/react';
import { motion } from 'framer-motion';
import { FaUser, FaBan, FaTimes } from 'react-icons/fa';
import { toast } from 'sonner';
import { userService, type User } from '@/services/user.service';
import { useTranslations } from 'next-intl';

interface BanManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User;
  onStatusUpdate: () => Promise<void>;
}

export default function BanManagementModal({ 
  isOpen, 
  onClose, 
  user,
  onStatusUpdate
}: BanManagementModalProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const t = useTranslations('userBanManagementModal');

  const handleAction = async () => {
    setIsProcessing(true);
    try {
      if (user.is_banned) {
        await userService.unbanUser(user.id);
        toast.success(t('userUnbanned', { username: user.username }));
      } else {
        await userService.banUser(user.id);
        toast.success(t('userBanned', { username: user.username }));
      }
      await onStatusUpdate();
      onClose();
    } catch (error: unknown) {
      console.error('Ban management error:', error);
      if (axios.isAxiosError(error)) {
        toast.error(error.response?.data?.message || t('banStatusUpdateFailed'));
      } else {
        toast.error(t('banStatusUpdateFailed'));
      }
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-xl transition-all">
                <div className="bg-gray-900 text-white p-6 rounded-t-2xl">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <FaBan className="text-red-500" />
                      {user.is_banned ? t('unbanUser') : t('banUser')}
                    </h3>
                    <button
                      onClick={onClose}
                      className="text-gray-300 hover:text-white transition-colors"
                    >
                      <FaTimes className="w-5 h-5" />
                    </button>
                  </div>
                </div>

                <div className="p-6">
                  <div className="space-y-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <FaUser className="text-gray-400 w-5 h-5" />
                        <p className="text-sm font-medium text-gray-600">{t('userInformation')}</p>
                      </div>
                      <div className="pl-7">
                        <p className="text-lg font-semibold text-gray-900">@{user.username}</p>
                        <p className="text-sm text-gray-500 capitalize">{user.role}</p>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <p className="text-sm text-yellow-800">
                        {user.is_banned 
                          ? t('thisActionWillUnban')
                          : t('thisActionWillBan')}
                      </p>
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                      onClick={onClose}
                    >
                      {t('cancel')}
                    </button>
                    <motion.button
                      whileTap={{ scale: 0.95 }}
                      type="button"
                      className={`inline-flex justify-center items-center gap-2 rounded-md border border-transparent px-4 py-2 text-sm font-medium text-white focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 ${
                        user.is_banned
                          ? 'bg-green-600 hover:bg-green-700 focus-visible:ring-green-500'
                          : 'bg-red-600 hover:bg-red-700 focus-visible:ring-red-500'
                      }`}
                      onClick={handleAction}
                      disabled={isProcessing}
                    >
                      <FaBan />
                      {isProcessing 
                        ? t('processing')
                        : user.is_banned 
                          ? t('unbanUser')
                          : t('banUser')
                      }
                    </motion.button>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
